<?php
/**
 * API路由入口文件
 */

require_once __DIR__ . '/../config/bootstrap.php';

// 获取请求信息
$method = Utils::getRequestMethod();
$uri = Utils::getRequestUri();

// 移除API前缀
$path = str_replace('/api', '', $uri);
$path = trim($path, '/');

// 路由分发
try {
    switch (true) {
        // 统计接口
        case $path === 'stats' && $method === 'GET':
            require_once __DIR__ . '/endpoints/stats.php';
            break;
            
        // 令牌列表
        case $path === 'tokens' && $method === 'GET':
            require_once __DIR__ . '/endpoints/tokens.php';
            break;

        // 随机获取可用令牌
        case $path === 'tokens/random' && $method === 'GET':
            require_once __DIR__ . '/endpoints/get_random_token.php';
            break;

        // 创建令牌
        case $path === 'tokens' && $method === 'POST':
            require_once __DIR__ . '/endpoints/create_token.php';
            break;
            
        // 批量删除令牌
        case $path === 'tokens/batch' && $method === 'DELETE':
            require_once __DIR__ . '/endpoints/batch_delete.php';
            break;
            
        // 单个令牌操作
        case preg_match('/^tokens\/(\d+)$/', $path, $matches):
            $tokenId = $matches[1];
            switch ($method) {
                case 'GET':
                    require_once __DIR__ . '/endpoints/get_token.php';
                    break;
                case 'PUT':
                    require_once __DIR__ . '/endpoints/update_token.php';
                    break;
                case 'DELETE':
                    require_once __DIR__ . '/endpoints/delete_token.php';
                    break;
                default:
                    Utils::jsonResponse(null, 405, '不支持的请求方法');
            }
            break;
            
        // 更新令牌状态
        case preg_match('/^tokens\/(\d+)\/status$/', $path, $matches) && $method === 'PATCH':
            $tokenId = $matches[1];
            require_once __DIR__ . '/endpoints/update_status.php';
            break;
            
        default:
            Utils::jsonResponse(null, 404, 'API接口不存在');
    }
} catch (Exception $e) {
    Utils::jsonResponse(null, 500, '服务器内部错误: ' . $e->getMessage());
}
