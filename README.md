# 令牌管理系统

一个基于原生PHP + MySQL + HTML + Tailwind CSS构建的令牌管理系统。

## 功能特性

### 核心功能
- ✅ 令牌的增删改查操作
- ✅ 批量删除和状态管理
- ✅ 实时统计数据展示
- ✅ 搜索和筛选功能
- ✅ 分页显示
- ✅ 数据导出（CSV格式）
- ✅ 一键复制令牌信息

### 统计功能
- 今天添加的令牌数量
- 昨天添加的令牌数量
- 前天添加的令牌数量
- 总令牌数、已使用数、未使用数

### API接口
- `GET /api/stats` - 获取统计数据
- `GET /api/tokens` - 获取令牌列表（支持分页、搜索、筛选）
- `GET /api/tokens/{id}` - 获取单个令牌详情
- `POST /api/tokens` - 创建新令牌
- `PUT /api/tokens/{id}` - 更新令牌信息
- `PATCH /api/tokens/{id}/status` - 更新令牌使用状态
- `DELETE /api/tokens/{id}` - 删除单个令牌
- `DELETE /api/tokens/batch` - 批量删除令牌

## 技术栈

- **后端**: 原生PHP 7.4+
- **数据库**: MySQL 5.7+
- **前端**: HTML5 + Tailwind CSS + Alpine.js
- **Web服务器**: Apache（支持.htaccess）

## 安装部署

### 1. 环境要求
- PHP 7.4 或更高版本
- MySQL 5.7 或更高版本
- Apache Web服务器（启用mod_rewrite）

### 2. 数据库配置
1. 创建MySQL数据库
2. 导入数据库结构：
```bash
mysql -u username -p database_name < database/schema.sql
```

3. 修改数据库配置文件 `config/database.php`：
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'token_management');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 3. Web服务器配置
确保Apache启用了mod_rewrite模块，并且项目目录允许.htaccess文件生效。

### 4. 访问系统
在浏览器中访问项目根目录即可使用系统。

## 项目结构

```
├── api/                    # API接口目录
│   ├── endpoints/          # 具体接口实现
│   └── index.php          # API路由入口
├── assets/                 # 静态资源
│   ├── css/               # CSS文件
│   └── js/                # JavaScript文件
├── config/                 # 配置文件
│   ├── bootstrap.php      # 系统引导文件
│   ├── config.php         # 基础配置
│   └── database.php       # 数据库配置
├── database/              # 数据库相关
│   └── schema.sql         # 数据库结构文件
├── views/                 # 视图文件（预留）
├── .htaccess             # URL重写规则
├── index.php             # 主页面
└── README.md             # 说明文档
```

## 数据库结构

### tokens表
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INT | 主键，自增 |
| email | VARCHAR(255) | 邮箱账号 |
| session | TEXT | cookie中的session值 |
| did | VARCHAR(255) | 设备标识 |
| auth0 | TEXT | auth0认证信息 |
| did_compat | VARCHAR(255) | 兼容性设备标识 |
| auth0_compat | TEXT | 兼容性auth0信息 |
| _GRECAPTCHA | TEXT | Google reCAPTCHA令牌 |
| auth_url | TEXT | 授权验证URL，用于cookie验证流程 |
| is_used | BOOLEAN | 是否已被使用 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## 使用说明

### 1. 查看统计
系统首页顶部显示各项统计数据，包括今天、昨天、前天的新增令牌数量，以及总数、已使用数、未使用数。

### 2. 管理令牌
- **添加令牌**: 点击"添加令牌"按钮，填写表单信息
- **编辑令牌**: 点击表格中的编辑图标
- **查看详情**: 点击表格中的查看图标
- **复制信息**: 点击复制图标将令牌信息复制到剪贴板
- **切换状态**: 点击状态切换图标改变令牌使用状态
- **删除令牌**: 点击删除图标删除单个令牌

### 3. 批量操作
- 选择多个令牌后，可以进行批量删除操作
- 支持全选/取消全选功能

### 4. 搜索筛选
- 在搜索框中输入关键词可搜索邮箱、会话或设备ID
- 使用状态筛选器可以按使用状态筛选令牌

### 5. 数据导出
点击"导出"按钮可以将所有令牌数据导出为CSV文件。

## 开发说明

### API响应格式
所有API接口返回统一的JSON格式：
```json
{
    "status": "success|error",
    "data": {},
    "message": "响应消息",
    "timestamp": "2024-01-01 12:00:00"
}
```

### 错误处理
系统包含完整的错误处理机制，所有错误都会以友好的方式展示给用户。

### 安全性
- 所有输入数据都经过清理和验证
- 使用PDO预处理语句防止SQL注入
- 邮箱格式验证

## 许可证

MIT License

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现所有核心功能
- 完整的API接口
- 响应式用户界面

