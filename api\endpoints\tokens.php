<?php
/**
 * 获取令牌列表接口
 */

$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$pageSize = isset($_GET['page_size']) ? min(MAX_PAGE_SIZE, max(1, intval($_GET['page_size']))) : DEFAULT_PAGE_SIZE;
$search = isset($_GET['search']) ? Utils::sanitizeInput($_GET['search']) : '';
$isUsed = isset($_GET['is_used']) ? ($_GET['is_used'] === 'true' ? 1 : ($_GET['is_used'] === 'false' ? 0 : null)) : null;

$tokenModel = new TokenModel();
$result = $tokenModel->getTokens($page, $pageSize, $search, $isUsed);

Utils::jsonResponse($result, 200, '获取令牌列表成功');
