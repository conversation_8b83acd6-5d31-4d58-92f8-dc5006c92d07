{"info": {"_postman_id": "token-management-api", "name": "令牌管理系统 API", "description": "令牌管理系统的完整API接口集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost/api", "type": "string"}], "item": [{"name": "统计数据", "item": [{"name": "获取统计数据", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/stats", "host": ["{{baseUrl}}"], "path": ["stats"]}}}]}, {"name": "令牌管理", "item": [{"name": "获取令牌列表", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/tokens?page=1&page_size=20", "host": ["{{baseUrl}}"], "path": ["tokens"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "20"}, {"key": "search", "value": "", "disabled": true}, {"key": "is_used", "value": "", "disabled": true}]}}}, {"name": "搜索令牌", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/tokens?search=<EMAIL>", "host": ["{{baseUrl}}"], "path": ["tokens"], "query": [{"key": "search", "value": "<EMAIL>"}]}}}, {"name": "筛选已使用令牌", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/tokens?is_used=true", "host": ["{{baseUrl}}"], "path": ["tokens"], "query": [{"key": "is_used", "value": "true"}]}}}, {"name": "获取单个令牌", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/tokens/1", "host": ["{{baseUrl}}"], "path": ["tokens", "1"]}}}, {"name": "创建令牌", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"session\": \"new_session_token\",\n    \"did\": \"new_device_001\",\n    \"auth0\": \"new_auth0_token\",\n    \"did_compat\": \"new_compat_device\",\n    \"auth0_compat\": \"new_compat_auth0\",\n    \"_GRECAPTCHA\": \"new_grecaptcha_token\",\n    \"auth_url\": \"https://auth.example.com/verify?token=xyz789\",\n    \"is_used\": false\n}"}, "url": {"raw": "{{baseUrl}}/tokens", "host": ["{{baseUrl}}"], "path": ["tokens"]}}}, {"name": "创建令牌(仅邮箱)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/tokens", "host": ["{{baseUrl}}"], "path": ["tokens"]}}}, {"name": "更新令牌", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"session\": \"updated_session_token\",\n    \"did\": \"updated_device_001\",\n    \"auth0\": \"updated_auth0_token\",\n    \"did_compat\": \"updated_compat_device\",\n    \"auth0_compat\": \"updated_compat_auth0\",\n    \"_GRECAPTCHA\": \"updated_grecaptcha_token\",\n    \"auth_url\": \"https://auth.example.com/verify?token=updated123\",\n    \"is_used\": true\n}"}, "url": {"raw": "{{baseUrl}}/tokens/1", "host": ["{{baseUrl}}"], "path": ["tokens", "1"]}}}, {"name": "更新令牌状态为已使用", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"is_used\": true\n}"}, "url": {"raw": "{{baseUrl}}/tokens/1/status", "host": ["{{baseUrl}}"], "path": ["tokens", "1", "status"]}}}, {"name": "更新令牌状态为未使用", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"is_used\": false\n}"}, "url": {"raw": "{{baseUrl}}/tokens/1/status", "host": ["{{baseUrl}}"], "path": ["tokens", "1", "status"]}}}, {"name": "删除单个令牌", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/tokens/1", "host": ["{{baseUrl}}"], "path": ["tokens", "1"]}}}, {"name": "批量删除令牌", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"ids\": [1, 2, 3, 4, 5]\n}"}, "url": {"raw": "{{baseUrl}}/tokens/batch", "host": ["{{baseUrl}}"], "path": ["tokens", "batch"]}}}]}]}