<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>令牌管理系统 - 简约白色主题</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/minimal-white-theme.css">
</head>
<body>
    <div class="main-container">
        <!-- 页面标题 -->
        <div class="text-center">
            <h1 class="page-title text-4xl">令牌管理系统</h1>
            <p class="page-subtitle">高端简约白色主题 - 管理和监控系统令牌的使用情况</p>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stats-card">
                <div class="stats-icon primary">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stats-label">今天</div>
                <div class="stats-number">15</div>
            </div>
            
            <div class="stats-card">
                <div class="stats-icon success">
                    <i class="fas fa-calendar-minus"></i>
                </div>
                <div class="stats-label">昨天</div>
                <div class="stats-number">23</div>
            </div>
            
            <div class="stats-card">
                <div class="stats-icon warning">
                    <i class="fas fa-calendar-week"></i>
                </div>
                <div class="stats-label">前天</div>
                <div class="stats-number">18</div>
            </div>
            
            <div class="stats-card">
                <div class="stats-icon gray">
                    <i class="fas fa-database"></i>
                </div>
                <div class="stats-label">总计</div>
                <div class="stats-number">1,247</div>
            </div>
            
            <div class="stats-card">
                <div class="stats-icon info">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stats-label">已使用</div>
                <div class="stats-number">892</div>
            </div>
            
            <div class="stats-card">
                <div class="stats-icon success">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stats-label">未使用</div>
                <div class="stats-number">355</div>
            </div>
        </div>

        <!-- 操作栏 -->
        <div class="search-controls">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="flex flex-col sm:flex-row gap-4">
                    <!-- 搜索框 -->
                    <div class="relative">
                        <input type="text" placeholder="搜索邮箱、会话或设备ID..." class="form-input pl-10 w-full sm:w-80">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                    
                    <!-- 状态筛选 -->
                    <select class="form-input">
                        <option value="">全部状态</option>
                        <option value="false">未使用</option>
                        <option value="true">已使用</option>
                    </select>
                </div>
                
                <div class="flex gap-3">
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        添加令牌
                    </button>
                    
                    <button class="btn btn-success">
                        <i class="fas fa-download"></i>
                        导出
                    </button>
                    
                    <button class="btn btn-danger">
                        <i class="fas fa-trash"></i>
                        批量删除 (3)
                    </button>
                </div>
            </div>
        </div>

        <!-- 令牌表格 -->
        <div class="table-container">
            <div class="overflow-x-auto">
                <table class="table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </th>
                            <th>ID</th>
                            <th>邮箱</th>
                            <th>设备ID</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="font-mono">1001</td>
                            <td><EMAIL></td>
                            <td class="font-mono">device_123</td>
                            <td>
                                <span class="status-badge success">未使用</span>
                            </td>
                            <td>2025-08-04 10:30:00</td>
                            <td>
                                <div class="flex gap-1">
                                    <button class="action-btn primary" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="action-btn success" title="标记为已使用">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="action-btn danger" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="font-mono">1002</td>
                            <td><EMAIL></td>
                            <td class="font-mono">device_456</td>
                            <td>
                                <span class="status-badge danger">已使用</span>
                            </td>
                            <td>2025-08-04 09:15:00</td>
                            <td>
                                <div class="flex gap-1">
                                    <button class="action-btn primary" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="action-btn success" title="标记为未使用">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button class="action-btn danger" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="font-mono">1003</td>
                            <td><EMAIL></td>
                            <td class="font-mono">device_789</td>
                            <td>
                                <span class="status-badge success">未使用</span>
                            </td>
                            <td>2025-08-04 08:45:00</td>
                            <td>
                                <div class="flex gap-1">
                                    <button class="action-btn primary" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="action-btn success" title="标记为已使用">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="action-btn danger" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    <span>显示第 </span>
                    <span class="font-semibold">1</span>
                    <span> 到 </span>
                    <span class="font-semibold">20</span>
                    <span> 条，共 </span>
                    <span class="font-semibold">1,247</span>
                    <span> 条记录</span>
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn">2</button>
                    <button class="pagination-btn">3</button>
                    <button class="pagination-btn">...</button>
                    <button class="pagination-btn">63</button>
                    
                    <button class="pagination-btn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 设计说明 -->
        <div class="mt-12 search-controls">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">简约白色主题设计</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-gray-700">
                <div>
                    <h3 class="text-lg font-semibold mb-2 text-gray-800">🎨 设计特色</h3>
                    <ul class="space-y-1 text-sm">
                        <li>• 纯白色背景，高端简约</li>
                        <li>• 克制的色彩使用，专业大气</li>
                        <li>• 清晰的视觉层次和对比度</li>
                        <li>• 简洁的线条和边框设计</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-2 text-gray-800">✨ 交互体验</h3>
                    <ul class="space-y-1 text-sm">
                        <li>• 简化的悬停和点击效果</li>
                        <li>• 克制的动画过渡</li>
                        <li>• 优秀的可读性和对比度</li>
                        <li>• 响应式设计适配</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/minimal-interactions.js"></script>
</body>
</html>
