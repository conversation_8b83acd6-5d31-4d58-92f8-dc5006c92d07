# 令牌管理系统 API 接口文档

## 基础信息

- **基础URL**: `http://your-domain.com/api`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **时区**: Asia/Shanghai

## 统一响应格式

所有API接口返回统一的JSON格式：

```json
{
    "status": "success|error",
    "data": {},
    "message": "响应消息",
    "timestamp": "2025-08-04 12:00:00"
}
```

## 接口列表

### 1. 获取统计数据

**接口地址**: `GET /api/stats`

**功能描述**: 获取令牌的统计信息，包括今天、昨天、前天的新增数量以及总数、已使用数、未使用数

**请求参数**: 无

**请求示例**:
```bash
curl -X GET "http://your-domain.com/api/stats" \
  -H "Content-Type: application/json"
```

**响应示例**:
```json
{
    "status": "success",
    "data": {
        "today": 5,
        "yesterday": 8,
        "day_before_yesterday": 3,
        "total": 150,
        "used": 45,
        "unused": 105
    },
    "message": "获取统计数据成功",
    "timestamp": "2025-08-04 12:00:00"
}
```

### 2. 获取令牌列表

**接口地址**: `GET /api/tokens`

**功能描述**: 获取令牌列表，支持分页、搜索和筛选

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码 |
| page_size | int | 否 | 20 | 每页数量(最大100) |
| search | string | 否 | "" | 搜索关键词(邮箱、session、授权URL、验证码) |
| is_used | string | 否 | null | 使用状态("true"/"false") |

**请求示例**:
```bash
# 基础请求
curl -X GET "http://your-domain.com/api/tokens" \
  -H "Content-Type: application/json"

# 带分页
curl -X GET "http://your-domain.com/api/tokens?page=2&page_size=10" \
  -H "Content-Type: application/json"

# 带搜索
curl -X GET "http://your-domain.com/api/tokens?search=<EMAIL>" \
  -H "Content-Type: application/json"

# 筛选已使用的令牌
curl -X GET "http://your-domain.com/api/tokens?is_used=true" \
  -H "Content-Type: application/json"

# 组合查询
curl -X GET "http://your-domain.com/api/tokens?page=1&page_size=5&search=test&is_used=false" \
  -H "Content-Type: application/json"
```

**响应示例**:
```json
{
    "status": "success",
    "data": {
        "tokens": [
            {
                "id": 1,
                "email": "<EMAIL>",
                "session": "session_token_1",
                "auth_url": "https://auth.example.com/verify?token=abc123",
                "code_verifier": "verifier_code_1",
                "is_used": false,
                "created_at": "2025-08-04 10:00:00",
                "updated_at": "2025-08-04 10:00:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "page_size": 20,
            "total_items": 150,
            "total_pages": 8,
            "has_prev": false,
            "has_next": true
        }
    },
    "message": "获取令牌列表成功",
    "timestamp": "2025-08-04 12:00:00"
}
```

### 3. 随机获取可用令牌

**接口地址**: `GET /api/tokens/random`

**功能描述**: 随机获取一个可用的令牌，支持自动标记为已使用

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| auto_use | string | 否 | false | 是否自动标记为已使用("true"/"false") |

**请求示例**:
```bash
# 仅获取可用令牌，不标记为已使用
curl -X GET "http://your-domain.com/api/tokens/random" \
  -H "Content-Type: application/json"

# 获取令牌并自动标记为已使用
curl -X GET "http://your-domain.com/api/tokens/random?auto_use=true" \
  -H "Content-Type: application/json"
```

**响应示例**:
```json
{
    "status": "success",
    "data": {
        "id": 3,
        "email": "<EMAIL>",
        "session": "session_token_3",
        "auth_url": "https://auth.example.com/verify?token=ghi789",
        "code_verifier": "verifier_code_3",
        "is_used": false,
        "created_at": "2025-08-04 09:30:00",
        "updated_at": "2025-08-04 09:30:00"
    },
    "message": "成功获取可用令牌",
    "timestamp": "2025-08-04 12:00:00"
}
```

**auto_use=true时的响应示例**:
```json
{
    "status": "success",
    "data": {
        "id": 3,
        "email": "<EMAIL>",
        "session": "session_token_3",
        "auth_url": "https://auth.example.com/verify?token=ghi789",
        "code_verifier": "verifier_code_3",
        "is_used": true,
        "created_at": "2025-08-04 09:30:00",
        "updated_at": "2025-08-04 12:00:00"
    },
    "message": "成功获取并使用令牌",
    "timestamp": "2025-08-04 12:00:00"
}
```

**错误响应示例**:
```json
{
    "status": "error",
    "data": null,
    "message": "没有可用的令牌",
    "timestamp": "2025-08-04 12:00:00"
}
```

### 4. 获取单个令牌详情

**接口地址**: `GET /api/tokens/{id}`

**功能描述**: 根据ID获取单个令牌的详细信息

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 令牌ID |

**请求示例**:
```bash
curl -X GET "http://your-domain.com/api/tokens/1" \
  -H "Content-Type: application/json"
```

**响应示例**:
```json
{
    "status": "success",
    "data": {
        "id": 1,
        "email": "<EMAIL>",
        "session": "session_token_1",
        "auth_url": "https://auth.example.com/verify?token=abc123",
        "code_verifier": "verifier_code_1",
        "is_used": false,
        "created_at": "2025-08-04 10:00:00",
        "updated_at": "2025-08-04 10:00:00"
    },
    "message": "获取令牌详情成功",
    "timestamp": "2025-08-04 12:00:00"
}
```

### 5. 创建新令牌

**接口地址**: `POST /api/tokens`

**功能描述**: 创建一个新的令牌

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| email | string | 是 | 邮箱地址 |
| session | string | 否 | Session值 |
| auth_url | string | 否 | 授权验证URL，用于OAuth2认证流程 |
| code_verifier | string | 否 | OAuth2 PKCE流程验证码 |
| is_used | boolean | 否 | 是否已使用(默认false) |

**请求示例**:
```bash

# 完整请求
curl -X POST "http://your-domain.com/api/tokens" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "session": "new_session_token",
    "auth_url": "https://auth.example.com/verify?token=xyz789",
    "code_verifier": "new_verifier_code_123",
    "is_used": false
  }'
```

**响应示例**:
```json
{
    "status": "success",
    "data": {
        "id": 151,
        "email": "<EMAIL>",
        "session": "new_session_token",
        "auth_url": "https://auth.example.com/verify?token=xyz789",
        "code_verifier": "new_verifier_code_123",
        "is_used": false,
        "created_at": "2025-08-04 12:00:00",
        "updated_at": "2025-08-04 12:00:00"
    },
    "message": "令牌创建成功",
    "timestamp": "2025-08-04 12:00:00"
}
```

### 6. 更新令牌信息

**接口地址**: `PUT /api/tokens/{id}`

**功能描述**: 更新指定ID的令牌信息

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 令牌ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| email | string | 是 | 邮箱地址 |
| session | string | 否 | Session值 |
| auth_url | string | 否 | 授权验证URL，用于OAuth2认证流程 |
| code_verifier | string | 否 | OAuth2 PKCE流程验证码 |
| is_used | boolean | 否 | 是否已使用 |

**请求示例**:
```bash
curl -X PUT "http://your-domain.com/api/tokens/1" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "session": "updated_session_token",
    "auth_url": "https://auth.example.com/verify?token=updated123",
    "code_verifier": "updated_verifier_code",
    "is_used": true
  }'
```

**响应示例**:
```json
{
    "status": "success",
    "data": {
        "id": 1,
        "email": "<EMAIL>",
        "session": "updated_session_token",
        "auth_url": "https://auth.example.com/verify?token=updated123",
        "code_verifier": "updated_verifier_code",
        "is_used": true,
        "created_at": "2025-08-04 10:00:00",
        "updated_at": "2025-08-04 12:05:00"
    },
    "message": "令牌更新成功",
    "timestamp": "2025-08-04 12:05:00"
}
```

### 7. 更新令牌使用状态

**接口地址**: `PATCH /api/tokens/{id}/status`

**功能描述**: 仅更新令牌的使用状态

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 令牌ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| is_used | boolean | 是 | 是否已使用 |

**请求示例**:
```bash
# 标记为已使用
curl -X PATCH "http://your-domain.com/api/tokens/1/status" \
  -H "Content-Type: application/json" \
  -d '{
    "is_used": true
  }'

# 标记为未使用
curl -X PATCH "http://your-domain.com/api/tokens/1/status" \
  -H "Content-Type: application/json" \
  -d '{
    "is_used": false
  }'
```

**响应示例**:
```json
{
    "status": "success",
    "data": {
        "id": 1,
        "email": "<EMAIL>",
        "session": "session_token_1",
        "auth_url": "https://auth.example.com/verify?token=abc123",
        "code_verifier": "verifier_code_1",
        "is_used": true,
        "created_at": "2025-08-04 10:00:00",
        "updated_at": "2025-08-04 12:10:00"
    },
    "message": "令牌状态更新成功",
    "timestamp": "2025-08-04 12:10:00"
}
```

### 8. 删除单个令牌

**接口地址**: `DELETE /api/tokens/{id}`

**功能描述**: 删除指定ID的令牌

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 令牌ID |

**请求示例**:
```bash
curl -X DELETE "http://your-domain.com/api/tokens/1" \
  -H "Content-Type: application/json"
```

**响应示例**:
```json
{
    "status": "success",
    "data": null,
    "message": "令牌删除成功",
    "timestamp": "2025-08-04 12:15:00"
}
```

### 9. 批量删除令牌

**接口地址**: `DELETE /api/tokens/batch`

**功能描述**: 批量删除多个令牌

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 要删除的令牌ID数组 |

**请求示例**:
```bash
curl -X DELETE "http://your-domain.com/api/tokens/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [1, 2, 3, 4, 5]
  }'
```

**响应示例**:
```json
{
    "status": "success",
    "data": {
        "deleted_count": 5,
        "requested_count": 5
    },
    "message": "成功删除 5 个令牌",
    "timestamp": "2025-08-04 12:20:00"
}
```

## 错误响应示例

### 400 Bad Request - 参数错误
```json
{
    "status": "error",
    "data": null,
    "message": "邮箱不能为空",
    "timestamp": "2025-08-04 12:00:00"
}
```

### 404 Not Found - 资源不存在
```json
{
    "status": "error",
    "data": null,
    "message": "令牌不存在",
    "timestamp": "2025-08-04 12:00:00"
}
```

### 405 Method Not Allowed - 方法不支持
```json
{
    "status": "error",
    "data": null,
    "message": "不支持的请求方法",
    "timestamp": "2025-08-04 12:00:00"
}
```

### 500 Internal Server Error - 服务器错误
```json
{
    "status": "error",
    "data": null,
    "message": "服务器内部错误: 数据库连接失败",
    "timestamp": "2025-08-04 12:00:00"
}
```

## 数据验证规则

### 邮箱验证
- 必须符合标准邮箱格式
- 使用PHP的`filter_var($email, FILTER_VALIDATE_EMAIL)`进行验证

### 数据清理
- 字符串数据会经过`htmlspecialchars()`处理，防止XSS攻击和HTML注入
- 布尔值和数字类型保持原始类型不变
- 布尔值会自动转换为数据库兼容的整数格式（true->1, false->0）

### 布尔值处理
系统支持多种布尔值表示形式，都会正确转换为数据库的整数格式：
- **真值**: `true`, `"true"`, `"True"`, `"TRUE"`, `"1"`, `1`, `"yes"`, `"on"`
- **假值**: `false`, `"false"`, `"False"`, `"FALSE"`, `"0"`, `0`, `"no"`, `"off"`, `""`

### 分页限制
- 页码最小值：1
- 每页数量最小值：1
- 每页数量最大值：100（由MAX_PAGE_SIZE常量定义）
- 默认每页数量：20（由DEFAULT_PAGE_SIZE常量定义）

## 使用说明

1. **CORS支持**: API支持跨域请求，允许所有来源
2. **字符编码**: 所有请求和响应都使用UTF-8编码
3. **时间格式**: 所有时间字段使用"Y-m-d H:i:s"格式
4. **布尔值**: 在URL参数中，布尔值使用字符串"true"/"false"表示
5. **数据库事务**: 批量操作使用数据库事务确保数据一致性

## 测试工具推荐

- **Postman**: 图形化API测试工具
- **curl**: 命令行工具
- **HTTPie**: 更友好的命令行HTTP客户端
- **Insomnia**: 现代化的API测试工具
```

