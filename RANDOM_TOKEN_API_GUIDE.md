# 随机获取令牌API使用指南

## 📋 接口概述

新增的随机获取令牌API专为注册机等自动化场景设计，提供了高效、安全的令牌获取机制。

## 🔗 接口信息

**接口地址**: `GET /api/tokens/random`

**功能描述**: 随机获取一个可用的令牌，支持自动标记为已使用

## 📝 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| auto_use | string | 否 | false | 是否自动标记为已使用("true"/"false") |

## 🎯 使用场景

### 1. 注册机自动化场景
```bash
# 获取令牌并自动标记为已使用
curl -X GET "http://your-domain.com/api/tokens/random?auto_use=true" \
  -H "Content-Type: application/json"
```

### 2. 令牌预览场景
```bash
# 仅获取令牌信息，不标记为已使用
curl -X GET "http://your-domain.com/api/tokens/random" \
  -H "Content-Type: application/json"
```

## 📊 响应示例

### 成功响应（仅获取）
```json
{
    "status": "success",
    "data": {
        "id": 3,
        "email": "<EMAIL>",
        "session": "session_token_3",
        "auth_url": "https://auth.example.com/verify?token=ghi789",
        "code_verifier": "verifier_code_3",
        "is_used": false,
        "created_at": "2025-08-04 09:30:00",
        "updated_at": "2025-08-04 09:30:00"
    },
    "message": "成功获取可用令牌",
    "timestamp": "2025-08-04 12:00:00"
}
```

### 成功响应（获取并使用）
```json
{
    "status": "success",
    "data": {
        "id": 3,
        "email": "<EMAIL>",
        "session": "session_token_3",
        "auth_url": "https://auth.example.com/verify?token=ghi789",
        "code_verifier": "verifier_code_3",
        "is_used": true,
        "created_at": "2025-08-04 09:30:00",
        "updated_at": "2025-08-04 12:00:00"
    },
    "message": "成功获取并使用令牌",
    "timestamp": "2025-08-04 12:00:00"
}
```

### 错误响应（无可用令牌）
```json
{
    "status": "error",
    "data": null,
    "message": "没有可用的令牌",
    "timestamp": "2025-08-04 12:00:00"
}
```

## 💻 代码示例

### JavaScript/Node.js
```javascript
// 注册机场景：获取并使用令牌
async function getTokenForRegistration() {
    try {
        const response = await fetch('/api/tokens/random?auto_use=true');
        const data = await response.json();
        
        if (data.status === 'success') {
            const token = data.data;
            console.log('获取到令牌:', token);
            
            // 使用令牌进行注册
            await registerWithToken(token);
            
            return token;
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        console.error('获取令牌失败:', error.message);
        return null;
    }
}

// 预览场景：仅获取令牌信息
async function previewAvailableToken() {
    try {
        const response = await fetch('/api/tokens/random');
        const data = await response.json();
        
        if (data.status === 'success') {
            const token = data.data;
            console.log('可用令牌:', token);
            return token;
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        console.error('获取令牌失败:', error.message);
        return null;
    }
}
```

### Python
```python
import requests
import json

def get_random_token(auto_use=False):
    """获取随机令牌"""
    url = "http://your-domain.com/api/tokens/random"
    params = {"auto_use": "true"} if auto_use else {}
    
    try:
        response = requests.get(url, params=params)
        data = response.json()
        
        if data["status"] == "success":
            token = data["data"]
            print(f"获取到令牌: {token['email']}")
            return token
        else:
            print(f"获取失败: {data['message']}")
            return None
            
    except Exception as e:
        print(f"请求异常: {str(e)}")
        return None

# 使用示例
token = get_random_token(auto_use=True)  # 获取并使用
if token:
    print(f"令牌ID: {token['id']}, 邮箱: {token['email']}")
```

### PHP
```php
<?php
function getRandomToken($autoUse = false) {
    $url = "http://your-domain.com/api/tokens/random";
    if ($autoUse) {
        $url .= "?auto_use=true";
    }
    
    $response = file_get_contents($url);
    $data = json_decode($response, true);
    
    if ($data['status'] === 'success') {
        $token = $data['data'];
        echo "获取到令牌: " . $token['email'] . "\n";
        return $token;
    } else {
        echo "获取失败: " . $data['message'] . "\n";
        return null;
    }
}

// 使用示例
$token = getRandomToken(true); // 获取并使用
if ($token) {
    echo "令牌ID: " . $token['id'] . ", 状态: " . ($token['is_used'] ? '已使用' : '未使用') . "\n";
}
?>
```

## 🔒 技术特性

### 1. 数据库事务保护
- 使用数据库事务确保获取和标记操作的原子性
- 避免并发访问时的数据不一致问题

### 2. 随机选择算法
- 使用MySQL的`RAND()`函数实现真随机选择
- 避免总是获取相同的令牌

### 3. 错误处理机制
- 完善的异常处理和回滚机制
- 清晰的错误信息返回

## ⚠️ 注意事项

1. **并发安全**: 接口使用数据库事务保证并发安全性
2. **性能考虑**: 大量数据时`ORDER BY RAND()`可能影响性能，可考虑优化
3. **令牌耗尽**: 当没有可用令牌时会返回404错误
4. **状态一致性**: `auto_use=true`时确保令牌状态正确更新

## 🚀 最佳实践

1. **注册机使用**: 建议使用`auto_use=true`避免重复使用
2. **错误处理**: 始终检查响应状态和错误信息
3. **重试机制**: 在无可用令牌时实现适当的重试逻辑
4. **日志记录**: 记录令牌使用情况便于追踪和调试

## 📈 性能优化建议

1. **索引优化**: 确保`is_used`字段有索引
2. **连接池**: 使用数据库连接池提高并发性能
3. **缓存策略**: 可考虑缓存可用令牌数量
4. **批量处理**: 大量请求时考虑批量获取机制

---

**创建时间**: 2025-01-08
**适用版本**: v1.0+
**维护状态**: ✅ 活跃维护
