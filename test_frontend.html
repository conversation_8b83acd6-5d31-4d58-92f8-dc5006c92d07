<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化令牌管理系统 - 前端测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">简化令牌管理系统 - 前端测试</h1>
            <p class="text-gray-600">验证简化后的数据结构和界面功能</p>
        </div>

        <!-- 测试数据结构 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">数据结构测试</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-medium text-gray-700 mb-2">简化前字段 (已删除)</h3>
                    <ul class="text-sm text-red-600 space-y-1">
                        <li>❌ did (设备标识)</li>
                        <li>❌ auth0 (auth0认证信息)</li>
                        <li>❌ did_compat (兼容性设备标识)</li>
                        <li>❌ auth0_compat (兼容性auth0信息)</li>
                        <li>❌ _GRECAPTCHA (Google reCAPTCHA令牌)</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-medium text-gray-700 mb-2">简化后字段 (保留/新增)</h3>
                    <ul class="text-sm text-green-600 space-y-1">
                        <li>✅ id (主键，自增)</li>
                        <li>✅ email (邮箱账号)</li>
                        <li>✅ session (cookie中的session值)</li>
                        <li>✅ auth_url (授权验证URL)</li>
                        <li>🆕 code_verifier (OAuth2 PKCE流程验证码)</li>
                        <li>✅ is_used (是否已被使用)</li>
                        <li>✅ created_at (创建时间)</li>
                        <li>✅ updated_at (更新时间)</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 模拟令牌表格 -->
        <div class="bg-white rounded-lg shadow overflow-hidden mb-6">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold">简化后的令牌表格</h2>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">邮箱</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">授权URL</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><EMAIL></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span>https://auth.example.com/verify?token=abc123</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    未使用
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-01 10:00:00</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><EMAIL></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span>https://auth.example.com/verify?token=def456</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    已使用
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-01 11:00:00</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 模拟表单 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">简化后的令牌表单</h2>
            <form class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">邮箱 *</label>
                    <input type="email" value="<EMAIL>" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Session</label>
                    <textarea rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">session_token_example</textarea>
                </div>

                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">授权验证URL</label>
                    <input type="url" value="https://auth.example.com/verify?token=example123"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <p class="text-xs text-gray-500 mt-1">用于OAuth2认证流程的授权URL</p>
                </div>

                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Code Verifier</label>
                    <input type="text" value="example_verifier_code_123"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <p class="text-xs text-gray-500 mt-1">用于OAuth2 PKCE流程的验证码</p>
                </div>

                <div class="md:col-span-2">
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">已使用</span>
                    </label>
                </div>
            </form>
        </div>

        <!-- 测试结果 -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-6">
            <div class="flex items-center">
                <i class="fas fa-check-circle text-green-600 text-xl mr-3"></i>
                <div>
                    <h3 class="text-lg font-semibold text-green-800">简化完成</h3>
                    <p class="text-green-700">令牌管理系统已成功简化，数据模型更加清晰，界面更加简洁。</p>
                    <ul class="mt-2 text-sm text-green-600 space-y-1">
                        <li>✅ 数据库结构已简化</li>
                        <li>✅ 后端代码已更新</li>
                        <li>✅ 前端界面已修改</li>
                        <li>✅ JavaScript代码已更新</li>
                        <li>✅ 导出功能已调整</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
