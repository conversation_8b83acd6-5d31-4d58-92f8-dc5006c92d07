/* 令牌管理系统 - 高端简约白色主题 */

/* 设计令牌 - 色彩系统 */
:root {
    /* 主色调 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --primary-light: #dbeafe;
    
    /* 中性色 */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* 状态色 */
    --success-color: #059669;
    --success-light: #d1fae5;
    --warning-color: #d97706;
    --warning-light: #fef3c7;
    --danger-color: #dc2626;
    --danger-light: #fee2e2;
    --info-color: #0891b2;
    --info-light: #cffafe;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* 边框半径 */
    --radius-sm: 4px;
    --radius: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    
    /* 间距 */
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;
    
    /* 过渡 */
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 全局重置 */
* {
    transition: var(--transition);
}

/* 页面基础样式 */
body {
    background-color: var(--gray-50);
    color: var(--gray-900);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 主容器 */
.main-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-xl) var(--spacing-lg);
    background-color: var(--white);
    min-height: 100vh;
}

/* 页面标题 */
.page-title {
    color: var(--gray-900);
    font-weight: 700;
    letter-spacing: -0.025em;
    margin-bottom: var(--spacing);
}

.page-subtitle {
    color: var(--gray-600);
    font-weight: 400;
    margin-bottom: var(--spacing-2xl);
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stats-card {
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.stats-card:hover {
    border-color: var(--gray-300);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing);
    font-size: 1.25rem;
}

.stats-icon.primary {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.stats-icon.success {
    background-color: var(--success-light);
    color: var(--success-color);
}

.stats-icon.warning {
    background-color: var(--warning-light);
    color: var(--warning-color);
}

.stats-icon.info {
    background-color: var(--info-light);
    color: var(--info-color);
}

.stats-icon.gray {
    background-color: var(--gray-100);
    color: var(--gray-600);
}

.stats-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--spacing-xs);
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    line-height: 1;
}

/* 搜索和控制区域 */
.search-controls {
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

/* 表单元素 */
.form-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    background-color: var(--white);
    color: var(--gray-900);
    font-size: 0.875rem;
    transition: var(--transition);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.form-input::placeholder {
    color: var(--gray-400);
}

/* 按钮系统 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing);
    border-radius: var(--radius);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid transparent;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-outline {
    background-color: transparent;
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.btn-outline:hover:not(:disabled) {
    background-color: var(--gray-50);
    border-color: var(--gray-400);
}

.btn-success {
    background-color: var(--success-color);
    color: var(--white);
    border-color: var(--success-color);
}

.btn-success:hover:not(:disabled) {
    background-color: #047857;
    border-color: #047857;
}

.btn-danger {
    background-color: var(--danger-color);
    color: var(--white);
    border-color: var(--danger-color);
}

.btn-danger:hover:not(:disabled) {
    background-color: #b91c1c;
    border-color: #b91c1c;
}

/* 操作按钮 */
.action-btn {
    width: 32px;
    height: 32px;
    border-radius: var(--radius);
    border: 1px solid var(--gray-300);
    background-color: var(--white);
    color: var(--gray-600);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.action-btn:hover {
    border-color: var(--gray-400);
    color: var(--gray-700);
    background-color: var(--gray-50);
}

.action-btn.primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.action-btn.primary:hover {
    background-color: var(--primary-light);
}

.action-btn.success {
    border-color: var(--success-color);
    color: var(--success-color);
}

.action-btn.success:hover {
    background-color: var(--success-light);
}

.action-btn.danger {
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.action-btn.danger:hover {
    background-color: var(--danger-light);
}

/* 表格 */
.table-container {
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table thead {
    background-color: var(--gray-50);
}

.table th {
    padding: var(--spacing) var(--spacing-md);
    text-align: left;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-700);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid var(--gray-200);
}

.table td {
    padding: var(--spacing) var(--spacing-md);
    border-bottom: 1px solid var(--gray-100);
    color: var(--gray-900);
    font-size: 0.875rem;
}

.table tbody tr:hover {
    background-color: var(--gray-50);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* 状态标签 */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.success {
    background-color: var(--success-light);
    color: var(--success-color);
}

.status-badge.danger {
    background-color: var(--danger-light);
    color: var(--danger-color);
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    padding: var(--spacing);
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 42rem;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: between;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.modal-close {
    width: 32px;
    height: 32px;
    border-radius: var(--radius);
    border: none;
    background-color: transparent;
    color: var(--gray-400);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.modal-close:hover {
    background-color: var(--gray-100);
    color: var(--gray-600);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing);
}

/* 表单组 */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-xs);
}

.form-textarea {
    min-height: 80px;
    resize: vertical;
}

/* 分页 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: between;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
    background-color: var(--white);
}

.pagination-info {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.pagination-btn {
    width: 32px;
    height: 32px;
    border-radius: var(--radius);
    border: 1px solid var(--gray-300);
    background-color: var(--white);
    color: var(--gray-600);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.pagination-btn:hover:not(:disabled) {
    border-color: var(--gray-400);
    background-color: var(--gray-50);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

/* 通知 */
.notification {
    position: fixed;
    top: var(--spacing);
    right: var(--spacing);
    z-index: 50;
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius);
    padding: var(--spacing);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing);
    max-width: 400px;
}

.notification.success {
    border-color: var(--success-color);
}

.notification.success .notification-icon {
    color: var(--success-color);
}

.notification.error {
    border-color: var(--danger-color);
}

.notification.error .notification-icon {
    color: var(--danger-color);
}

.notification-message {
    flex: 1;
    font-size: 0.875rem;
    color: var(--gray-900);
}

.notification-close {
    width: 20px;
    height: 20px;
    border: none;
    background: none;
    color: var(--gray-400);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    color: var(--gray-600);
}

/* 工具类 */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.font-mono {
    font-family: ui-monospace, SFMono-Regular, 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        padding: var(--spacing) var(--spacing);
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing);
    }

    .search-controls {
        padding: var(--spacing);
    }

    .table th,
    .table td {
        padding: var(--spacing-sm);
    }

    .modal-content {
        margin: var(--spacing);
        max-width: calc(100vw - 2rem);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing);
    }

    .pagination {
        flex-direction: column;
        gap: var(--spacing);
        align-items: stretch;
    }

    .pagination-controls {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 1.875rem;
    }

    .stats-number {
        font-size: 1.5rem;
    }

    .btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8125rem;
    }

    .action-btn {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }
}

/* 加载状态 */
.loading {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--gray-600);
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--gray-300);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--gray-500);
}

.empty-state-icon {
    font-size: 3rem;
    color: var(--gray-300);
    margin-bottom: var(--spacing);
}

.empty-state-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-xs);
}

.empty-state-description {
    font-size: 0.875rem;
    color: var(--gray-500);
}
