<?php
/**
 * 批量删除令牌接口
 */

$data = Utils::getPostData();

if (empty($data['ids']) || !is_array($data['ids'])) {
    Utils::jsonResponse(null, 400, '请提供要删除的令牌ID列表');
}

// 验证所有ID都是数字
$ids = array_filter($data['ids'], 'is_numeric');
if (empty($ids)) {
    Utils::jsonResponse(null, 400, '无效的令牌ID列表');
}

$tokenModel = new TokenModel();
$deletedCount = $tokenModel->deleteTokens($ids);

Utils::jsonResponse([
    'deleted_count' => $deletedCount,
    'requested_count' => count($data['ids'])
], 200, "成功删除 {$deletedCount} 个令牌");
