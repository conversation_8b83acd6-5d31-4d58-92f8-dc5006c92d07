-- 令牌管理系统数据库结构
-- 创建数据库
CREATE DATABASE IF NOT EXISTS poolpool CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE poolpool;

-- 创建tokens表
CREATE TABLE IF NOT EXISTS tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL COMMENT '邮箱账号',
    session TEXT COMMENT 'cookie中的session值',
    auth_url VARCHAR(500) COMMENT '授权验证URL',
    code_verifier VARCHAR(128) COMMENT 'OAuth2 PKCE流程验证码',
    is_used BOOLEAN DEFAULT FALSE COMMENT '是否已被使用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_email (email),
    INDEX idx_is_used (is_used),
    INDEX idx_created_at (created_at),
    INDEX idx_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='令牌管理表';

-- 插入测试数据
INSERT INTO tokens (email, session, auth_url, code_verifier, is_used) VALUES
('<EMAIL>', 'session_token_1', 'https://auth.example.com/verify?token=abc123', 'verifier_code_1', FALSE),
('<EMAIL>', 'session_token_2', 'https://auth.example.com/verify?token=def456', 'verifier_code_2', TRUE),
('<EMAIL>', 'session_token_3', 'https://auth.example.com/verify?token=ghi789', 'verifier_code_3', FALSE),
('<EMAIL>', 'session_token_4', 'https://auth.example.com/verify?token=jkl012', 'verifier_code_4', TRUE),
('<EMAIL>', 'session_token_5', 'https://auth.example.com/verify?token=mno345', 'verifier_code_5', FALSE);
