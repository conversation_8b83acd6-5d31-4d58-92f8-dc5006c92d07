-- 令牌管理系统数据库结构
-- 创建数据库
CREATE DATABASE IF NOT EXISTS poolpool CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE poolpool;

-- 创建tokens表
CREATE TABLE IF NOT EXISTS tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL COMMENT '邮箱账号',
    session TEXT COMMENT 'cookie中的session值',
    did VARCHAR(255) COMMENT '设备标识',
    auth0 TEXT COMMENT 'auth0认证信息',
    did_compat VARCHAR(255) COMMENT '兼容性设备标识',
    auth0_compat TEXT COMMENT '兼容性auth0信息',
    _GRECAPTCHA TEXT COMMENT 'Google reCAPTCHA令牌',
    is_used BOOLEAN DEFAULT FALSE COMMENT '是否已被使用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_email (email),
    INDEX idx_is_used (is_used),
    INDEX idx_created_at (created_at),
    INDEX idx_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='令牌管理表';

-- 插入测试数据
INSERT INTO tokens (email, session, did, auth0, did_compat, auth0_compat, _GRECAPTCHA, is_used) VALUES
('<EMAIL>', 'session_token_1', 'device_001', 'auth0_token_1', 'compat_device_001', 'compat_auth0_1', 'grecaptcha_token_1', FALSE),
('<EMAIL>', 'session_token_2', 'device_002', 'auth0_token_2', 'compat_device_002', 'compat_auth0_2', 'grecaptcha_token_2', TRUE),
('<EMAIL>', 'session_token_3', 'device_003', 'auth0_token_3', 'compat_device_003', 'compat_auth0_3', 'grecaptcha_token_3', FALSE),
('<EMAIL>', 'session_token_4', 'device_004', 'auth0_token_4', 'compat_device_004', 'compat_auth0_4', 'grecaptcha_token_4', TRUE),
('<EMAIL>', 'session_token_5', 'device_005', 'auth0_token_5', 'compat_device_005', 'compat_auth0_5', 'grecaptcha_token_5', FALSE);
