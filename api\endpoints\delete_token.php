<?php
/**
 * 删除令牌接口
 */

if (!isset($tokenId) || !is_numeric($tokenId)) {
    Utils::jsonResponse(null, 400, '无效的令牌ID');
}

$tokenModel = new TokenModel();

// 检查令牌是否存在
$existingToken = $tokenModel->getTokenById($tokenId);
if (!$existingToken) {
    Utils::jsonResponse(null, 404, '令牌不存在');
}

$result = $tokenModel->deleteToken($tokenId);

if ($result) {
    Utils::jsonResponse(null, 200, '令牌删除成功');
} else {
    Utils::jsonResponse(null, 500, '令牌删除失败');
}
