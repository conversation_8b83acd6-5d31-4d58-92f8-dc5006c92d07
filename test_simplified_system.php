<?php
/**
 * 简化令牌管理系统测试脚本
 */

// 引入配置文件
require_once 'config/bootstrap.php';

echo "=== 简化令牌管理系统测试 ===\n\n";

try {
    $tokenModel = new TokenModel();
    
    // 测试1: 获取统计数据
    echo "1. 测试统计数据获取...\n";
    $stats = $tokenModel->getStats();
    echo "统计结果: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    // 测试2: 获取令牌列表
    echo "2. 测试令牌列表获取...\n";
    $result = $tokenModel->getTokens(1, 5);
    echo "令牌数量: " . count($result['tokens']) . "\n";
    if (!empty($result['tokens'])) {
        $firstToken = $result['tokens'][0];
        echo "第一个令牌字段: " . implode(', ', array_keys($firstToken)) . "\n";
    }
    echo "\n";
    
    // 测试3: 创建新令牌
    echo "3. 测试创建新令牌...\n";
    $newTokenData = [
        'email' => '<EMAIL>',
        'session' => 'test_session_new',
        'auth_url' => 'https://auth.example.com/verify?token=test123',
        'code_verifier' => 'test_verifier_code',
        'is_used' => false
    ];
    
    $newTokenId = $tokenModel->createToken($newTokenData);
    echo "新令牌ID: $newTokenId\n\n";
    
    // 测试4: 获取新创建的令牌
    echo "4. 测试获取单个令牌...\n";
    $token = $tokenModel->getTokenById($newTokenId);
    if ($token) {
        echo "令牌详情: " . json_encode($token, JSON_UNESCAPED_UNICODE) . "\n";
    }
    echo "\n";
    
    // 测试5: 更新令牌
    echo "5. 测试更新令牌...\n";
    $updateData = [
        'email' => '<EMAIL>',
        'session' => 'updated_session',
        'auth_url' => 'https://auth.example.com/verify?token=updated123',
        'code_verifier' => 'updated_verifier_code',
        'is_used' => true
    ];
    
    $updateResult = $tokenModel->updateToken($newTokenId, $updateData);
    echo "更新结果: " . ($updateResult ? "成功" : "失败") . "\n\n";
    
    // 测试6: 搜索功能
    echo "6. 测试搜索功能...\n";
    $searchResult = $tokenModel->getTokens(1, 10, 'test_updated');
    echo "搜索结果数量: " . count($searchResult['tokens']) . "\n\n";
    
    // 测试7: 状态筛选
    echo "7. 测试状态筛选...\n";
    $usedTokens = $tokenModel->getTokens(1, 10, '', 1);
    $unusedTokens = $tokenModel->getTokens(1, 10, '', 0);
    echo "已使用令牌数量: " . count($usedTokens['tokens']) . "\n";
    echo "未使用令牌数量: " . count($unusedTokens['tokens']) . "\n\n";
    
    // 测试8: 删除令牌
    echo "8. 测试删除令牌...\n";
    $deleteResult = $tokenModel->deleteToken($newTokenId);
    echo "删除结果: " . ($deleteResult ? "成功" : "失败") . "\n\n";
    
    echo "=== 所有测试完成 ===\n";
    echo "✅ 简化后的系统功能正常！\n";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
}
?>
