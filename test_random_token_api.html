<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>随机获取令牌API测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">随机获取令牌API测试</h1>
            <p class="text-gray-600">测试新增的随机获取可用令牌接口功能</p>
        </div>

        <!-- API接口说明 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">新增接口说明</h2>
            <div class="space-y-4">
                <div>
                    <h3 class="font-medium text-gray-700 mb-2">接口地址</h3>
                    <code class="bg-gray-100 px-2 py-1 rounded">GET /api/tokens/random</code>
                </div>
                
                <div>
                    <h3 class="font-medium text-gray-700 mb-2">功能说明</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 随机获取一个可用的令牌（is_used = false）</li>
                        <li>• 支持自动标记为已使用（auto_use=true参数）</li>
                        <li>• 使用数据库事务确保数据一致性</li>
                        <li>• 适用于注册机等自动化场景</li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-medium text-gray-700 mb-2">请求参数</h3>
                    <table class="min-w-full text-sm">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-3 py-2 text-left">参数名</th>
                                <th class="px-3 py-2 text-left">类型</th>
                                <th class="px-3 py-2 text-left">必填</th>
                                <th class="px-3 py-2 text-left">说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="px-3 py-2">auto_use</td>
                                <td class="px-3 py-2">string</td>
                                <td class="px-3 py-2">否</td>
                                <td class="px-3 py-2">是否自动标记为已使用("true"/"false")</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 测试按钮 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">接口测试</h2>
            <div class="flex gap-4 mb-4">
                <button onclick="testRandomToken(false)" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-random mr-2"></i>获取随机令牌
                </button>
                <button onclick="testRandomToken(true)" 
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-check mr-2"></i>获取并使用令牌
                </button>
                <button onclick="clearResults()" 
                        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-trash mr-2"></i>清空结果
                </button>
            </div>
            
            <!-- 结果显示 -->
            <div id="results" class="space-y-4"></div>
        </div>

        <!-- 使用场景示例 -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">使用场景示例</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium text-gray-700 mb-2">注册机场景</h3>
                    <pre class="bg-gray-100 p-3 rounded text-sm overflow-x-auto"><code>// 获取并使用一个令牌进行注册
fetch('/api/tokens/random?auto_use=true')
  .then(response => response.json())
  .then(data => {
    if (data.status === 'success') {
      const token = data.data;
      // 使用令牌进行注册
      registerWithToken(token);
    }
  });</code></pre>
                </div>
                
                <div>
                    <h3 class="font-medium text-gray-700 mb-2">预览场景</h3>
                    <pre class="bg-gray-100 p-3 rounded text-sm overflow-x-auto"><code>// 仅获取令牌信息，不标记为已使用
fetch('/api/tokens/random')
  .then(response => response.json())
  .then(data => {
    if (data.status === 'success') {
      const token = data.data;
      // 显示令牌信息供用户确认
      showTokenPreview(token);
    }
  });</code></pre>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function testRandomToken(autoUse) {
            const resultsDiv = document.getElementById('results');
            const url = autoUse ? '/api/tokens/random?auto_use=true' : '/api/tokens/random';
            
            // 添加加载状态
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'bg-blue-50 border border-blue-200 rounded-lg p-4';
            loadingDiv.innerHTML = `
                <div class="flex items-center">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                    <span class="text-blue-800">正在${autoUse ? '获取并使用' : '获取'}令牌...</span>
                </div>
            `;
            resultsDiv.appendChild(loadingDiv);
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                
                // 移除加载状态
                resultsDiv.removeChild(loadingDiv);
                
                // 显示结果
                const resultDiv = document.createElement('div');
                const isSuccess = data.status === 'success';
                resultDiv.className = `border rounded-lg p-4 ${isSuccess ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`;
                
                resultDiv.innerHTML = `
                    <div class="flex items-start">
                        <i class="fas ${isSuccess ? 'fa-check-circle text-green-600' : 'fa-exclamation-circle text-red-600'} mt-1 mr-3"></i>
                        <div class="flex-1">
                            <h3 class="font-medium ${isSuccess ? 'text-green-800' : 'text-red-800'} mb-2">
                                ${autoUse ? '获取并使用令牌' : '获取随机令牌'} - ${isSuccess ? '成功' : '失败'}
                            </h3>
                            <p class="text-sm ${isSuccess ? 'text-green-700' : 'text-red-700'} mb-2">
                                ${data.message}
                            </p>
                            <div class="bg-gray-100 p-3 rounded text-sm">
                                <strong>完整响应:</strong>
                                <pre class="mt-1 whitespace-pre-wrap">${JSON.stringify(data, null, 2)}</pre>
                            </div>
                            <div class="text-xs text-gray-500 mt-2">
                                请求时间: ${new Date().toLocaleString()}
                            </div>
                        </div>
                    </div>
                `;
                
                resultsDiv.appendChild(resultDiv);
                
            } catch (error) {
                // 移除加载状态
                resultsDiv.removeChild(loadingDiv);
                
                // 显示错误
                const errorDiv = document.createElement('div');
                errorDiv.className = 'bg-red-50 border border-red-200 rounded-lg p-4';
                errorDiv.innerHTML = `
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-red-600 mt-1 mr-3"></i>
                        <div>
                            <h3 class="font-medium text-red-800 mb-2">请求失败</h3>
                            <p class="text-sm text-red-700">${error.message}</p>
                        </div>
                    </div>
                `;
                resultsDiv.appendChild(errorDiv);
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
