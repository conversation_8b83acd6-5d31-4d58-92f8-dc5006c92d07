<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>令牌管理系统 - 设计演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/modern-ui.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="main-container">
        <!-- 页面标题 -->
        <div class="mb-8 text-center">
            <h1 class="page-title text-5xl font-bold mb-4">令牌管理系统</h1>
            <p class="page-subtitle text-lg">现代化设计演示 - 管理和监控系统令牌的使用情况</p>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
            <div class="stats-card today">
                <div class="flex items-center">
                    <div class="stats-icon">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="ml-4">
                        <p class="stats-label">今天</p>
                        <p class="stats-number">15</p>
                    </div>
                </div>
            </div>
            
            <div class="stats-card yesterday">
                <div class="flex items-center">
                    <div class="stats-icon">
                        <i class="fas fa-calendar-minus"></i>
                    </div>
                    <div class="ml-4">
                        <p class="stats-label">昨天</p>
                        <p class="stats-number">23</p>
                    </div>
                </div>
            </div>
            
            <div class="stats-card day-before">
                <div class="flex items-center">
                    <div class="stats-icon">
                        <i class="fas fa-calendar-week"></i>
                    </div>
                    <div class="ml-4">
                        <p class="stats-label">前天</p>
                        <p class="stats-number">18</p>
                    </div>
                </div>
            </div>
            
            <div class="stats-card total">
                <div class="flex items-center">
                    <div class="stats-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="ml-4">
                        <p class="stats-label">总计</p>
                        <p class="stats-number">1,247</p>
                    </div>
                </div>
            </div>
            
            <div class="stats-card used">
                <div class="flex items-center">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="ml-4">
                        <p class="stats-label">已使用</p>
                        <p class="stats-number">892</p>
                    </div>
                </div>
            </div>
            
            <div class="stats-card unused">
                <div class="flex items-center">
                    <div class="stats-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="ml-4">
                        <p class="stats-label">未使用</p>
                        <p class="stats-number">355</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作栏 -->
        <div class="search-controls">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="flex flex-col sm:flex-row gap-4">
                    <!-- 搜索框 -->
                    <div class="relative">
                        <input type="text" 
                               placeholder="搜索邮箱、会话或设备ID..." 
                               class="form-input pl-10 pr-4 py-3 w-full sm:w-80">
                        <i class="fas fa-search absolute left-3 top-4 text-white opacity-70"></i>
                    </div>
                    
                    <!-- 状态筛选 -->
                    <select class="form-input px-4 py-3">
                        <option value="">全部状态</option>
                        <option value="false">未使用</option>
                        <option value="true">已使用</option>
                    </select>
                </div>
                
                <div class="flex gap-3">
                    <button class="btn-modern flex items-center gap-2">
                        <i class="fas fa-plus"></i>
                        添加令牌
                    </button>
                    
                    <button class="btn-modern btn-success flex items-center gap-2">
                        <i class="fas fa-download"></i>
                        导出
                    </button>
                    
                    <button class="btn-modern btn-danger flex items-center gap-2">
                        <i class="fas fa-trash"></i>
                        批量删除 (3)
                    </button>
                </div>
            </div>
        </div>

        <!-- 令牌表格 -->
        <div class="modern-table">
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead>
                        <tr>
                            <th class="px-6 py-4 text-left">
                                <input type="checkbox" class="rounded border-white/30 text-blue-400 focus:ring-blue-400 bg-white/10">
                            </th>
                            <th class="px-6 py-4 text-left">ID</th>
                            <th class="px-6 py-4 text-left">邮箱</th>
                            <th class="px-6 py-4 text-left">设备ID</th>
                            <th class="px-6 py-4 text-left">状态</th>
                            <th class="px-6 py-4 text-left">创建时间</th>
                            <th class="px-6 py-4 text-left">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="px-6 py-4">
                                <input type="checkbox" class="rounded border-white/30 text-blue-400 focus:ring-blue-400 bg-white/10">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap font-mono">1001</td>
                            <td class="px-6 py-4 whitespace-nowrap"><EMAIL></td>
                            <td class="px-6 py-4 whitespace-nowrap font-mono">device_123</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge unused">未使用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">2025-08-04 10:30:00</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="action-buttons">
                                    <button class="action-btn view" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn copy" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="action-btn toggle" title="标记为已使用">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="action-btn delete" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4">
                                <input type="checkbox" class="rounded border-white/30 text-blue-400 focus:ring-blue-400 bg-white/10">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap font-mono">1002</td>
                            <td class="px-6 py-4 whitespace-nowrap"><EMAIL></td>
                            <td class="px-6 py-4 whitespace-nowrap font-mono">device_456</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge used">已使用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">2025-08-04 09:15:00</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="action-buttons">
                                    <button class="action-btn view" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn copy" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="action-btn toggle" title="标记为未使用">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button class="action-btn delete" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4">
                                <input type="checkbox" class="rounded border-white/30 text-blue-400 focus:ring-blue-400 bg-white/10">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap font-mono">1003</td>
                            <td class="px-6 py-4 whitespace-nowrap"><EMAIL></td>
                            <td class="px-6 py-4 whitespace-nowrap font-mono">device_789</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge unused">未使用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">2025-08-04 08:45:00</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="action-buttons">
                                    <button class="action-btn view" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn edit" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn copy" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="action-btn toggle" title="标记为已使用">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="action-btn delete" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="pagination">
                <div class="hidden sm:flex sm:items-center sm:justify-between w-full">
                    <div>
                        <p class="text-sm text-white/80">
                            显示第 <span class="font-bold text-white">1</span> 
                            到 <span class="font-bold text-white">20</span> 
                            条，共 <span class="font-bold text-white">1,247</span> 条记录
                        </p>
                    </div>
                    <div class="flex gap-2">
                        <button class="pagination-btn" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn">...</button>
                        <button class="pagination-btn">63</button>
                        
                        <button class="pagination-btn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设计说明 -->
        <div class="mt-12 search-controls">
            <h2 class="text-2xl font-bold text-white mb-4">设计改善说明</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-white/90">
                <div>
                    <h3 class="text-lg font-semibold mb-2">✨ 视觉效果提升</h3>
                    <ul class="space-y-1 text-sm">
                        <li>• 渐变背景和玻璃拟态效果</li>
                        <li>• 现代化的色彩搭配</li>
                        <li>• 多层次阴影系统</li>
                        <li>• 流畅的动画过渡</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-2">🎯 用户体验优化</h3>
                    <ul class="space-y-1 text-sm">
                        <li>• 悬停效果和视觉反馈</li>
                        <li>• 响应式设计适配</li>
                        <li>• 统计数字动画效果</li>
                        <li>• 操作按钮组优化</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/animations.js"></script>
</body>
</html>
