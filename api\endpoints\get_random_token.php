<?php
/**
 * 随机获取可用令牌接口
 */

// 检查请求方法
if (Utils::getRequestMethod() !== 'GET') {
    Utils::jsonResponse(null, 405, '不支持的请求方法');
}

// 获取查询参数
$autoUse = isset($_GET['auto_use']) && $_GET['auto_use'] === 'true';

$tokenModel = new TokenModel();

try {
    if ($autoUse) {
        // 获取并自动标记为已使用
        $token = $tokenModel->getAndUseRandomToken();
        
        if (!$token) {
            Utils::jsonResponse(null, 404, '没有可用的令牌');
        }
        
        Utils::jsonResponse($token, 200, '成功获取并使用令牌');
    } else {
        // 仅获取，不标记为已使用
        $token = $tokenModel->getRandomAvailableToken();
        
        if (!$token) {
            Utils::jsonResponse(null, 404, '没有可用的令牌');
        }
        
        Utils::jsonResponse($token, 200, '成功获取可用令牌');
    }
} catch (Exception $e) {
    Utils::jsonResponse(null, 500, '服务器内部错误: ' . $e->getMessage());
}
?>
