<?php
/**
 * 系统引导文件
 */

// 引入配置文件
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/database.php';

// 设置CORS头
Utils::setCorsHeaders();

/**
 * 令牌模型类
 */
class TokenModel {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 获取令牌列表
     */
    public function getTokens($page = 1, $pageSize = DEFAULT_PAGE_SIZE, $search = '', $isUsed = null) {
        $offset = ($page - 1) * $pageSize;
        
        // 构建查询条件
        $where = [];
        $params = [];
        
        if (!empty($search)) {
            $where[] = "(email LIKE ? OR session LIKE ? OR auth_url LIKE ? OR code_verifier LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if ($isUsed !== null) {
            $where[] = "is_used = ?";
            $params[] = $isUsed;
        }
        
        $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
        
        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM tokens $whereClause";
        $total = $this->db->fetchOne($countSql, $params)['total'];
        
        // 获取数据
        $dataSql = "SELECT * FROM tokens $whereClause ORDER BY created_at DESC LIMIT $pageSize OFFSET $offset";
        $tokens = $this->db->fetchAll($dataSql, $params);
        
        return [
            'tokens' => $tokens,
            'pagination' => Utils::generatePagination($total, $page, $pageSize)
        ];
    }
    
    /**
     * 根据ID获取令牌
     */
    public function getTokenById($id) {
        $sql = "SELECT * FROM tokens WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }
    
    /**
     * 创建令牌
     */
    public function createToken($data) {
        $sql = "INSERT INTO tokens (email, session, auth_url, code_verifier, is_used)
                VALUES (?, ?, ?, ?, ?)";

        // 处理布尔值转换
        $isUsed = $this->convertToBoolean($data['is_used'] ?? false);

        $params = [
            $data['email'],
            $data['session'] ?? '',
            $data['auth_url'] ?? '',
            $data['code_verifier'] ?? '',
            $isUsed
        ];

        return $this->db->insert($sql, $params);
    }
    
    /**
     * 更新令牌
     */
    public function updateToken($id, $data) {
        $sql = "UPDATE tokens SET
                email = ?, session = ?, auth_url = ?, code_verifier = ?, is_used = ?,
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?";

        // 处理布尔值转换
        $isUsed = $this->convertToBoolean($data['is_used'] ?? false);

        $params = [
            $data['email'],
            $data['session'] ?? '',
            $data['auth_url'] ?? '',
            $data['code_verifier'] ?? '',
            $isUsed,
            $id
        ];

        return $this->db->execute($sql, $params);
    }
    
    /**
     * 更新令牌状态
     */
    public function updateTokenStatus($id, $isUsed) {
        $sql = "UPDATE tokens SET is_used = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        return $this->db->execute($sql, [$isUsed, $id]);
    }
    
    /**
     * 删除令牌
     */
    public function deleteToken($id) {
        $sql = "DELETE FROM tokens WHERE id = ?";
        return $this->db->execute($sql, [$id]);
    }
    
    /**
     * 批量删除令牌
     */
    public function deleteTokens($ids) {
        if (empty($ids)) return 0;
        
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $sql = "DELETE FROM tokens WHERE id IN ($placeholders)";
        return $this->db->execute($sql, $ids);
    }
    
    /**
     * 获取统计数据
     */
    public function getStats() {
        $today = Utils::getTodayRange();
        $yesterday = Utils::getYesterdayRange();
        $dayBeforeYesterday = Utils::getDayBeforeYesterdayRange();
        
        $todayCount = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM tokens WHERE created_at BETWEEN ? AND ?",
            [$today['start'], $today['end']]
        )['count'];
        
        $yesterdayCount = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM tokens WHERE created_at BETWEEN ? AND ?",
            [$yesterday['start'], $yesterday['end']]
        )['count'];
        
        $dayBeforeYesterdayCount = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM tokens WHERE created_at BETWEEN ? AND ?",
            [$dayBeforeYesterday['start'], $dayBeforeYesterday['end']]
        )['count'];
        
        $totalCount = $this->db->fetchOne("SELECT COUNT(*) as count FROM tokens")['count'];
        $usedCount = $this->db->fetchOne("SELECT COUNT(*) as count FROM tokens WHERE is_used = 1")['count'];
        $unusedCount = $totalCount - $usedCount;
        
        return [
            'today' => (int)$todayCount,
            'yesterday' => (int)$yesterdayCount,
            'day_before_yesterday' => (int)$dayBeforeYesterdayCount,
            'total' => (int)$totalCount,
            'used' => (int)$usedCount,
            'unused' => (int)$unusedCount
        ];
    }

    /**
     * 转换布尔值
     * 处理各种可能的布尔值表示形式
     */
    private function convertToBoolean($value) {
        // 如果已经是布尔值，直接返回对应的整数
        if (is_bool($value)) {
            return $value ? 1 : 0;
        }

        // 如果是字符串，进行转换
        if (is_string($value)) {
            $value = strtolower(trim($value));
            if (in_array($value, ['true', '1', 'yes', 'on'])) {
                return 1;
            }
            if (in_array($value, ['false', '0', 'no', 'off', ''])) {
                return 0;
            }
        }

        // 如果是数字，转换为布尔值再转整数
        if (is_numeric($value)) {
            return intval($value) ? 1 : 0;
        }

        // 默认返回0（false）
        return 0;
    }
}


