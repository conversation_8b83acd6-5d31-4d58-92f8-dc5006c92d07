/**
 * 令牌管理系统 - 简约交互效果
 * 高端简约白色主题的克制动画和交互
 */

// 简化的通知系统
function showNotification(type, message) {
    // 移除现有通知
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-icon">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
        </div>
        <div class="notification-message">${message}</div>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    }, 4000);
}

// 简化的加载状态
function showLoading() {
    const loading = document.createElement('div');
    loading.className = 'loading-overlay';
    loading.innerHTML = `
        <div class="bg-white rounded-lg p-6 shadow-xl">
            <div class="flex items-center gap-3">
                <div class="loading-spinner"></div>
                <span class="text-gray-700">加载中...</span>
            </div>
        </div>
    `;
    document.body.appendChild(loading);
    return loading;
}

function hideLoading(loadingElement) {
    if (loadingElement && loadingElement.parentElement) {
        loadingElement.style.opacity = '0';
        setTimeout(() => {
            loadingElement.remove();
        }, 200);
    }
}

// 简化的表格行交互
function initTableInteractions() {
    // 为表格行添加简单的悬停效果
    document.addEventListener('mouseover', (event) => {
        const row = event.target.closest('.table tbody tr');
        if (row) {
            row.style.backgroundColor = 'var(--gray-50)';
        }
    });
    
    document.addEventListener('mouseout', (event) => {
        const row = event.target.closest('.table tbody tr');
        if (row) {
            row.style.backgroundColor = '';
        }
    });
}

// 简化的按钮交互
function initButtonInteractions() {
    // 为按钮添加简单的点击反馈
    document.addEventListener('mousedown', (event) => {
        const button = event.target.closest('.btn, .action-btn');
        if (button) {
            button.style.transform = 'scale(0.98)';
        }
    });
    
    document.addEventListener('mouseup', (event) => {
        const button = event.target.closest('.btn, .action-btn');
        if (button) {
            button.style.transform = '';
        }
    });
    
    // 鼠标离开时重置状态
    document.addEventListener('mouseleave', (event) => {
        const button = event.target.closest('.btn, .action-btn');
        if (button) {
            button.style.transform = '';
        }
    });
}

// 简化的卡片交互
function initCardInteractions() {
    document.addEventListener('mouseover', (event) => {
        const card = event.target.closest('.stats-card');
        if (card) {
            card.style.transform = 'translateY(-2px)';
            card.style.boxShadow = 'var(--shadow-md)';
        }
    });
    
    document.addEventListener('mouseout', (event) => {
        const card = event.target.closest('.stats-card');
        if (card) {
            card.style.transform = '';
            card.style.boxShadow = 'var(--shadow-sm)';
        }
    });
}

// 表单验证视觉反馈
function initFormValidation() {
    document.addEventListener('invalid', (event) => {
        const input = event.target;
        if (input.classList.contains('form-input')) {
            input.style.borderColor = 'var(--danger-color)';
            input.style.boxShadow = '0 0 0 3px var(--danger-light)';
        }
    }, true);
    
    document.addEventListener('input', (event) => {
        const input = event.target;
        if (input.classList.contains('form-input') && input.checkValidity()) {
            input.style.borderColor = '';
            input.style.boxShadow = '';
        }
    });
}

// 模态框焦点管理
function initModalFocus() {
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape') {
            // 查找打开的模态框并关闭
            const modal = document.querySelector('.modal-overlay[style*="display: block"], .modal-overlay:not([style*="display: none"])');
            if (modal) {
                const closeButton = modal.querySelector('.modal-close');
                if (closeButton) {
                    closeButton.click();
                }
            }
        }
    });
}

// 复制到剪贴板功能
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showNotification('success', '已复制到剪贴板');
    } catch (err) {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            showNotification('success', '已复制到剪贴板');
        } catch (err) {
            showNotification('error', '复制失败');
        }
        
        document.body.removeChild(textArea);
    }
}

// 简化的页面初始化
function initMinimalInteractions() {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            initTableInteractions();
            initButtonInteractions();
            initCardInteractions();
            initFormValidation();
            initModalFocus();
        });
    } else {
        initTableInteractions();
        initButtonInteractions();
        initCardInteractions();
        initFormValidation();
        initModalFocus();
    }
}

// 响应式表格处理
function initResponsiveTable() {
    function checkTableOverflow() {
        const tableContainers = document.querySelectorAll('.table-container');
        tableContainers.forEach(container => {
            const table = container.querySelector('.table');
            if (table && table.scrollWidth > container.clientWidth) {
                container.classList.add('overflow-x-auto');
            } else {
                container.classList.remove('overflow-x-auto');
            }
        });
    }
    
    // 初始检查
    checkTableOverflow();
    
    // 窗口大小改变时重新检查
    window.addEventListener('resize', checkTableOverflow);
}

// 平滑滚动到顶部
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// 导出函数供其他脚本使用
window.MinimalInteractions = {
    showNotification,
    showLoading,
    hideLoading,
    copyToClipboard,
    scrollToTop
};

// 自动初始化
initMinimalInteractions();
initResponsiveTable();
