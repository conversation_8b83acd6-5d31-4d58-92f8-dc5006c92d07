/**
 * 令牌管理系统 - 主要JavaScript文件
 */

function tokenManager() {
    return {
        // 数据状态
        tokens: [],
        stats: {
            today: 0,
            yesterday: 0,
            day_before_yesterday: 0,
            total: 0,
            used: 0,
            unused: 0
        },
        pagination: {
            current_page: 1,
            page_size: 20,
            total_items: 0,
            total_pages: 0,
            has_prev: false,
            has_next: false
        },
        
        // UI状态
        loading: false,
        showCreateModal: false,
        showEditModal: false,
        showViewModal: false,
        
        // 表单数据
        currentToken: {
            id: null,
            email: '',
            session: '',
            auth_url: '',
            code_verifier: '',
            is_used: false
        },
        viewTokenData: {},

        // 搜索和筛选
        searchQuery: '',
        statusFilter: '',
        selectedTokens: [],
        searchTimeout: null,
        
        // 通知
        notification: {
            show: false,
            type: 'success',
            message: ''
        },
        
        // 初始化
        async init() {
            await this.loadStats();
            await this.loadTokens();
        },
        
        // API请求封装
        async apiRequest(url, options = {}) {
            this.loading = true;
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.message || '请求失败');
                }
                
                return data;
            } catch (error) {
                this.showNotification('error', error.message);
                throw error;
            } finally {
                this.loading = false;
            }
        },
        
        // 加载统计数据
        async loadStats() {
            try {
                const response = await this.apiRequest('/api/stats');
                this.stats = response.data;
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        },
        
        // 加载令牌列表
        async loadTokens() {
            try {
                const params = new URLSearchParams({
                    page: this.pagination.current_page,
                    page_size: this.pagination.page_size
                });
                
                if (this.searchQuery) {
                    params.append('search', this.searchQuery);
                }
                
                if (this.statusFilter !== '') {
                    params.append('is_used', this.statusFilter);
                }
                
                const response = await this.apiRequest(`/api/tokens?${params}`);
                this.tokens = response.data.tokens;
                this.pagination = response.data.pagination;
                this.selectedTokens = [];
            } catch (error) {
                console.error('加载令牌列表失败:', error);
            }
        },
        
        // 搜索防抖
        debounceSearch() {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.pagination.current_page = 1;
                this.loadTokens();
            }, 500);
        },
        
        // 分页操作
        async goToPage(page) {
            this.pagination.current_page = page;
            await this.loadTokens();
        },
        
        async prevPage() {
            if (this.pagination.has_prev) {
                await this.goToPage(this.pagination.current_page - 1);
            }
        },
        
        async nextPage() {
            if (this.pagination.has_next) {
                await this.goToPage(this.pagination.current_page + 1);
            }
        },
        
        // 获取分页页码数组
        getPageNumbers() {
            const current = this.pagination.current_page;
            const total = this.pagination.total_pages;
            const pages = [];
            
            // 显示当前页前后2页
            const start = Math.max(1, current - 2);
            const end = Math.min(total, current + 2);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            
            return pages;
        },
        
        // 模态框操作
        closeModal() {
            this.showCreateModal = false;
            this.showEditModal = false;
            this.showViewModal = false;
            this.resetCurrentToken();
        },
        
        resetCurrentToken() {
            this.currentToken = {
                id: null,
                email: '',
                session: '',
                auth_url: '',
                code_verifier: '',
                is_used: false
            };
        },
        
        // 查看令牌
        viewToken(token) {
            this.viewTokenData = { ...token };
            this.showViewModal = true;
        },
        
        // 编辑令牌
        editToken(token) {
            this.currentToken = { ...token };
            this.showEditModal = true;
            this.showViewModal = false;
        },
        
        // 保存令牌
        async saveToken() {
            try {
                if (this.showCreateModal) {
                    await this.createToken();
                } else if (this.showEditModal) {
                    await this.updateToken();
                }
            } catch (error) {
                console.error('保存令牌失败:', error);
            }
        },
        
        // 创建令牌
        async createToken() {
            try {
                await this.apiRequest('/api/tokens', {
                    method: 'POST',
                    body: JSON.stringify(this.currentToken)
                });
                
                this.showNotification('success', '令牌创建成功');
                this.closeModal();
                await this.loadTokens();
                await this.loadStats();
            } catch (error) {
                console.error('创建令牌失败:', error);
            }
        },
        
        // 更新令牌
        async updateToken() {
            try {
                await this.apiRequest(`/api/tokens/${this.currentToken.id}`, {
                    method: 'PUT',
                    body: JSON.stringify(this.currentToken)
                });
                
                this.showNotification('success', '令牌更新成功');
                this.closeModal();
                await this.loadTokens();
                await this.loadStats();
            } catch (error) {
                console.error('更新令牌失败:', error);
            }
        },
        
        // 切换令牌状态
        async toggleTokenStatus(token) {
            try {
                await this.apiRequest(`/api/tokens/${token.id}/status`, {
                    method: 'PATCH',
                    body: JSON.stringify({ is_used: !token.is_used })
                });
                
                this.showNotification('success', '令牌状态更新成功');
                await this.loadTokens();
                await this.loadStats();
            } catch (error) {
                console.error('更新令牌状态失败:', error);
            }
        },
        
        // 删除令牌
        async deleteToken(tokenId) {
            if (!confirm('确定要删除这个令牌吗？')) {
                return;
            }
            
            try {
                await this.apiRequest(`/api/tokens/${tokenId}`, {
                    method: 'DELETE'
                });
                
                this.showNotification('success', '令牌删除成功');
                await this.loadTokens();
                await this.loadStats();
            } catch (error) {
                console.error('删除令牌失败:', error);
            }
        },
        
        // 批量删除
        async batchDelete() {
            if (this.selectedTokens.length === 0) {
                this.showNotification('error', '请选择要删除的令牌');
                return;
            }
            
            if (!confirm(`确定要删除选中的 ${this.selectedTokens.length} 个令牌吗？`)) {
                return;
            }
            
            try {
                await this.apiRequest('/api/tokens/batch', {
                    method: 'DELETE',
                    body: JSON.stringify({ ids: this.selectedTokens })
                });
                
                this.showNotification('success', `成功删除 ${this.selectedTokens.length} 个令牌`);
                this.selectedTokens = [];
                await this.loadTokens();
                await this.loadStats();
            } catch (error) {
                console.error('批量删除失败:', error);
            }
        },
        
        // 全选/取消全选
        toggleAllTokens(checked) {
            if (checked) {
                this.selectedTokens = this.tokens.map(token => token.id);
            } else {
                this.selectedTokens = [];
            }
        },
        
        // 复制令牌信息
        async copyToken(token) {
            const tokenInfo = {
                id: token.id,
                email: token.email,
                session: token.session,
                auth_url: token.auth_url,
                code_verifier: token.code_verifier,
                is_used: token.is_used,
                created_at: token.created_at,
                updated_at: token.updated_at
            };
            
            try {
                await navigator.clipboard.writeText(JSON.stringify(tokenInfo, null, 2));
                this.showNotification('success', '令牌信息已复制到剪贴板');
            } catch (error) {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = JSON.stringify(tokenInfo, null, 2);
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                this.showNotification('success', '令牌信息已复制到剪贴板');
            }
        },
        
        // 导出令牌
        async exportTokens() {
            try {
                // 获取所有令牌数据
                const response = await this.apiRequest('/api/tokens?page_size=10000');
                const allTokens = response.data.tokens;
                
                // 创建CSV内容
                const headers = ['ID', '邮箱', 'Session', '授权URL', 'Code Verifier', '状态', '创建时间', '更新时间'];
                const csvContent = [
                    headers.join(','),
                    ...allTokens.map(token => [
                        token.id,
                        `"${token.email}"`,
                        `"${token.session || ''}"`,
                        `"${token.auth_url || ''}"`,
                        `"${token.code_verifier || ''}"`,
                        token.is_used ? '已使用' : '未使用',
                        token.created_at,
                        token.updated_at
                    ].join(','))
                ].join('\n');
                
                // 下载文件
                const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `tokens_${new Date().toISOString().split('T')[0]}.csv`;
                link.click();
                
                this.showNotification('success', '令牌数据导出成功');
            } catch (error) {
                console.error('导出失败:', error);
            }
        },
        
        // 显示通知
        showNotification(type, message) {
            this.notification = {
                show: true,
                type: type,
                message: message
            };
            
            // 3秒后自动隐藏
            setTimeout(() => {
                this.notification.show = false;
            }, 3000);
        },
        
        // 格式化日期时间
        formatDateTime(datetime) {
            if (!datetime) return '-';
            return new Date(datetime).toLocaleString('zh-CN');
        }
    };
}




