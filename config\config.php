<?php
/**
 * 系统配置文件
 */

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 系统常量
define('APP_NAME', '令牌管理系统');
define('APP_VERSION', '1.0.0');
define('BASE_URL', '/');

// 分页设置
define('DEFAULT_PAGE_SIZE', 20);
define('MAX_PAGE_SIZE', 100);

// API响应格式
define('API_SUCCESS', 'success');
define('API_ERROR', 'error');

/**
 * 工具函数类
 */
class Utils {
    
    /**
     * 返回JSON响应
     */
    public static function jsonResponse($data, $status = 200, $message = '') {
        http_response_code($status);
        header('Content-Type: application/json; charset=utf-8');
        
        $response = [
            'status' => $status < 400 ? API_SUCCESS : API_ERROR,
            'data' => $data,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 获取POST数据
     */
    public static function getPostData() {
        $input = file_get_contents('php://input');
        return json_decode($input, true) ?: [];
    }
    
    /**
     * 验证邮箱格式
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * 清理输入数据
     */
    public static function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeInput'], $data);
        }

        // 保持布尔值和数字类型不变
        if (is_bool($data) || is_numeric($data)) {
            return $data;
        }

        // 只对字符串进行HTML转义
        if (is_string($data)) {
            return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
        }

        return $data;
    }
    
    /**
     * 生成分页信息
     */
    public static function generatePagination($total, $page, $pageSize) {
        $totalPages = ceil($total / $pageSize);
        
        return [
            'current_page' => (int)$page,
            'page_size' => (int)$pageSize,
            'total_items' => (int)$total,
            'total_pages' => (int)$totalPages,
            'has_prev' => $page > 1,
            'has_next' => $page < $totalPages
        ];
    }
    
    /**
     * 获取请求方法
     */
    public static function getRequestMethod() {
        return $_SERVER['REQUEST_METHOD'];
    }
    
    /**
     * 获取请求URI
     */
    public static function getRequestUri() {
        return parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    }
    
    /**
     * 设置CORS头
     */
    public static function setCorsHeaders() {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, PATCH, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');
        
        if (self::getRequestMethod() === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
    }
    
    /**
     * 格式化日期时间
     */
    public static function formatDateTime($datetime) {
        return date('Y-m-d H:i:s', strtotime($datetime));
    }
    
    /**
     * 获取今天开始和结束时间
     */
    public static function getTodayRange() {
        return [
            'start' => date('Y-m-d 00:00:00'),
            'end' => date('Y-m-d 23:59:59')
        ];
    }
    
    /**
     * 获取昨天开始和结束时间
     */
    public static function getYesterdayRange() {
        return [
            'start' => date('Y-m-d 00:00:00', strtotime('-1 day')),
            'end' => date('Y-m-d 23:59:59', strtotime('-1 day'))
        ];
    }
    
    /**
     * 获取前天开始和结束时间
     */
    public static function getDayBeforeYesterdayRange() {
        return [
            'start' => date('Y-m-d 00:00:00', strtotime('-2 days')),
            'end' => date('Y-m-d 23:59:59', strtotime('-2 days'))
        ];
    }
}
