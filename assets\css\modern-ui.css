/* 令牌管理系统 - 现代化UI样式 */

/* 全局变量定义 */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    
    --animation-speed: 0.3s;
    --border-radius: 16px;
    --border-radius-sm: 8px;
    --border-radius-lg: 24px;
}

/* 全局样式重置和基础设置 */
* {
    transition: all var(--animation-speed) cubic-bezier(0.4, 0, 0.2, 1);
}

/* 页面背景渐变 */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
    z-index: 1;
}

/* 背景动画 */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 主容器玻璃拟态效果 */
.main-container {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--glass-shadow);
    position: relative;
    z-index: 2;
    margin: 2rem;
    padding: 2rem;
}

/* 页面标题样式 */
.page-title {
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* 统计卡片现代化设计 */
.stats-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--glass-shadow);
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    transform: translateY(0);
    transition: all var(--animation-speed) cubic-bezier(0.4, 0, 0.2, 1);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform var(--animation-speed) ease;
}

.stats-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stats-card:hover::before {
    transform: scaleX(1);
}

/* 统计卡片特定颜色 */
.stats-card.today { --card-gradient: var(--primary-gradient); }
.stats-card.yesterday { --card-gradient: var(--success-gradient); }
.stats-card.day-before { --card-gradient: var(--warning-gradient); }
.stats-card.total { --card-gradient: var(--dark-gradient); }
.stats-card.used { --card-gradient: var(--danger-gradient); }
.stats-card.unused { --card-gradient: var(--info-gradient); }

.stats-card::before {
    background: var(--card-gradient);
}

/* 统计图标容器 */
.stats-icon {
    background: var(--card-gradient);
    border-radius: var(--border-radius-sm);
    padding: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: scale(1);
    transition: transform var(--animation-speed) ease;
}

.stats-card:hover .stats-icon {
    transform: scale(1.1) rotate(5deg);
}

.stats-icon i {
    color: white;
    font-size: 1.25rem;
}

/* 统计数字动画 */
.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
}

.stats-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 数字计数动画 */
@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.stats-number {
    animation: countUp 0.6s ease-out;
}

/* 搜索和控制区域 */
.search-controls {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--glass-shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

/* 现代化按钮设计 */
.btn-modern {
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--border-radius-sm);
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    position: relative;
    overflow: hidden;
    transform: translateY(0);
    transition: all var(--animation-speed) cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern:active {
    transform: translateY(0);
}

/* 按钮变体 */
.btn-success { background: var(--success-gradient); }
.btn-warning { background: var(--warning-gradient); }
.btn-danger { background: var(--danger-gradient); }
.btn-info { background: var(--info-gradient); }

/* 表格现代化设计 */
.modern-table {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--glass-shadow);
    overflow: hidden;
}

.modern-table thead {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
}

.modern-table thead th {
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1rem 1.5rem;
    border: none;
}

.modern-table tbody tr {
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--animation-speed) ease;
}

.modern-table tbody tr:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.01);
}

.modern-table tbody td {
    padding: 1rem 1.5rem;
    color: rgba(255, 255, 255, 0.9);
    border: none;
}

/* 状态标签现代化 */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.status-badge.used {
    background: var(--danger-gradient);
    color: white;
}

.status-badge.unused {
    background: var(--success-gradient);
    color: white;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.status-badge:hover::before {
    left: 100%;
}

/* 操作按钮组 */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.action-btn {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--animation-speed) cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.action-btn.view {
    background: var(--info-gradient);
    color: white;
}

.action-btn.edit {
    background: var(--warning-gradient);
    color: white;
}

.action-btn.delete {
    background: var(--danger-gradient);
    color: white;
}

.action-btn.copy {
    background: var(--success-gradient);
    color: white;
}

.action-btn.toggle {
    background: var(--primary-gradient);
    color: white;
}

.action-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-btn:active {
    transform: scale(0.95);
}

/* 模态框玻璃拟态设计 */
.modal-overlay {
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.modal-content {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--glass-shadow);
    position: relative;
    overflow: hidden;
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.modal-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.5rem;
    cursor: pointer;
    transition: all var(--animation-speed) ease;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.modal-close:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(90deg);
}

.modal-body {
    padding: 1rem 2rem 2rem;
}

/* 表单输入框现代化 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-sm);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    transition: all var(--animation-speed) ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-input:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.8);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
}

/* 分页控件现代化 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination-btn {
    padding: 0.5rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-sm);
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all var(--animation-speed) ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.pagination-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-2px);
}

.pagination-btn.active {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 通知消息样式 */
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 1000;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 600;
    box-shadow: var(--glass-shadow);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    transform: translateX(100%);
    transition: transform var(--animation-speed) cubic-bezier(0.4, 0, 0.2, 1);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: var(--success-gradient);
}

.notification.error {
    background: var(--danger-gradient);
}

.notification.warning {
    background: var(--warning-gradient);
}

.notification.info {
    background: var(--info-gradient);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        margin: 1rem;
        padding: 1rem;
    }

    .stats-card {
        padding: 1rem;
    }

    .stats-number {
        font-size: 1.5rem;
    }

    .modal-content {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }

    .modal-header,
    .modal-body {
        padding: 1rem;
    }

    .action-buttons {
        flex-wrap: wrap;
    }

    .action-btn {
        width: 2rem;
        height: 2rem;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 2rem;
    }

    .stats-card {
        text-align: center;
    }

    .stats-icon {
        margin: 0 auto 0.5rem;
    }

    .modern-table {
        font-size: 0.875rem;
    }

    .modern-table thead th,
    .modern-table tbody td {
        padding: 0.5rem;
    }
}

/* 波纹效果 */
.btn-modern,
.action-btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* 滚动动画 */
.animate-in {
    animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 悬停时的发光效果 */
.stats-card:hover {
    box-shadow:
        var(--glass-shadow),
        0 0 30px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.btn-modern:hover {
    box-shadow:
        0 8px 24px rgba(0, 0, 0, 0.2),
        0 0 20px rgba(102, 126, 234, 0.4);
}

/* 表格行悬停增强 */
.modern-table tbody tr:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
}

/* 模态框进入动画增强 */
.modal-content {
    animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 状态标签动画 */
.status-badge {
    animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
    0%, 100% {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
    50% {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 0 15px rgba(102, 126, 234, 0.3);
    }
}

/* 输入框聚焦动画 */
.form-input:focus {
    animation: inputFocus 0.3s ease;
}

@keyframes inputFocus {
    from {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
    to {
        transform: scale(1);
    }
}

/* 页面加载时的淡入效果 */
.main-container {
    animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 统计数字跳动效果 */
.stats-number {
    animation: numberBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes numberBounce {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 操作按钮组悬停效果 */
.action-buttons:hover .action-btn {
    transform: scale(0.9);
    opacity: 0.7;
}

.action-buttons .action-btn:hover {
    transform: scale(1.2) !important;
    opacity: 1 !important;
    z-index: 10;
}

/* 搜索框动画 */
.form-input[type="text"]:focus {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.02);
}

/* 分页按钮动画 */
.pagination-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pagination-btn:hover {
    transform: translateY(-2px) scale(1.05);
}

.pagination-btn.active {
    animation: activePagePulse 1s ease-in-out infinite alternate;
}

@keyframes activePagePulse {
    from {
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
    to {
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.6);
    }
}
