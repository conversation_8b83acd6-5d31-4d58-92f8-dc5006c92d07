<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>令牌管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <div x-data="tokenManager()" x-init="init()" class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">令牌管理系统</h1>
            <p class="text-gray-600">管理和监控系统令牌的使用情况</p>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <i class="fas fa-calendar-day text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">今天</p>
                        <p class="text-2xl font-bold text-gray-900" x-text="stats.today"></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <i class="fas fa-calendar-minus text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">昨天</p>
                        <p class="text-2xl font-bold text-gray-900" x-text="stats.yesterday"></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <i class="fas fa-calendar-week text-purple-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">前天</p>
                        <p class="text-2xl font-bold text-gray-900" x-text="stats.day_before_yesterday"></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-gray-100 rounded-lg">
                        <i class="fas fa-database text-gray-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">总计</p>
                        <p class="text-2xl font-bold text-gray-900" x-text="stats.total"></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 rounded-lg">
                        <i class="fas fa-check-circle text-red-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">已使用</p>
                        <p class="text-2xl font-bold text-gray-900" x-text="stats.used"></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 rounded-lg">
                        <i class="fas fa-clock text-yellow-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">未使用</p>
                        <p class="text-2xl font-bold text-gray-900" x-text="stats.unused"></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作栏 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6 border-b border-gray-200">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <!-- 搜索框 -->
                        <div class="relative">
                            <input type="text"
                                   x-model="searchQuery"
                                   @input="debounceSearch()"
                                   placeholder="搜索邮箱、会话、授权URL或验证码..."
                                   class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full sm:w-80">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        
                        <!-- 状态筛选 -->
                        <select x-model="statusFilter" @change="loadTokens()" 
                                class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">全部状态</option>
                            <option value="false">未使用</option>
                            <option value="true">已使用</option>
                        </select>
                    </div>
                    
                    <div class="flex gap-2">
                        <button @click="showCreateModal = true" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                            <i class="fas fa-plus"></i>
                            添加令牌
                        </button>
                        
                        <button @click="exportTokens()" 
                                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                            <i class="fas fa-download"></i>
                            导出
                        </button>
                        
                        <button @click="batchDelete()" 
                                x-show="selectedTokens.length > 0"
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                            <i class="fas fa-trash"></i>
                            批量删除 (<span x-text="selectedTokens.length"></span>)
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 令牌表格 -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left">
                                <input type="checkbox" 
                                       @change="toggleAllTokens($event.target.checked)"
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">邮箱</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">授权URL</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="token in tokens" :key="token.id">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <input type="checkbox" 
                                           :value="token.id"
                                           x-model="selectedTokens"
                                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="token.id"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="token.email"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <span x-text="token.auth_url ? token.auth_url.substring(0, 50) + (token.auth_url.length > 50 ? '...' : '') : '-'"></span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="token.is_used ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'" 
                                          class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                                        <span x-text="token.is_used ? '已使用' : '未使用'"></span>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="formatDateTime(token.created_at)"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex gap-2">
                                        <button @click="viewToken(token)" 
                                                class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button @click="editToken(token)" 
                                                class="text-green-600 hover:text-green-900">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button @click="copyToken(token)" 
                                                class="text-purple-600 hover:text-purple-900">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <button @click="toggleTokenStatus(token)" 
                                                class="text-yellow-600 hover:text-yellow-900">
                                            <i :class="token.is_used ? 'fas fa-undo' : 'fas fa-check'"></i>
                                        </button>
                                        <button @click="deleteToken(token.id)" 
                                                class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button @click="prevPage()" 
                            :disabled="!pagination.has_prev"
                            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
                        上一页
                    </button>
                    <button @click="nextPage()" 
                            :disabled="!pagination.has_next"
                            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
                        下一页
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span class="font-medium" x-text="(pagination.current_page - 1) * pagination.page_size + 1"></span> 
                            到 <span class="font-medium" x-text="Math.min(pagination.current_page * pagination.page_size, pagination.total_items)"></span> 
                            条，共 <span class="font-medium" x-text="pagination.total_items"></span> 条记录
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                            <button @click="prevPage()" 
                                    :disabled="!pagination.has_prev"
                                    class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            
                            <template x-for="page in getPageNumbers()" :key="page">
                                <button @click="goToPage(page)" 
                                        :class="page === pagination.current_page ? 'bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'"
                                        class="relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            
                            <button @click="nextPage()" 
                                    :disabled="!pagination.has_next"
                                    class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- 创建/编辑令牌模态框 -->
        <div x-show="showCreateModal || showEditModal"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100 transform scale-100"
                 x-transition:leave-end="opacity-0 transform scale-95"
                 class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-gray-900" x-text="showCreateModal ? '添加令牌' : '编辑令牌'"></h3>
                        <button @click="closeModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form @submit.prevent="saveToken()">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">邮箱 *</label>
                                <input type="email" x-model="currentToken.email" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>

                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Session</label>
                                <textarea x-model="currentToken.session" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                            </div>

                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">授权验证URL</label>
                                <input type="url" x-model="currentToken.auth_url"
                                       placeholder="https://auth.example.com/verify?token=..."
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="text-xs text-gray-500 mt-1">用于OAuth2认证流程的授权URL</p>
                            </div>

                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Code Verifier</label>
                                <input type="text" x-model="currentToken.code_verifier"
                                       placeholder="OAuth2 PKCE流程验证码"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="text-xs text-gray-500 mt-1">用于OAuth2 PKCE流程的验证码</p>
                            </div>

                            <div class="md:col-span-2">
                                <label class="flex items-center">
                                    <input type="checkbox" x-model="currentToken.is_used"
                                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">已使用</span>
                                </label>
                            </div>
                        </div>

                        <div class="flex justify-end gap-3 mt-6">
                            <button type="button" @click="closeModal()"
                                    class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                                取消
                            </button>
                            <button type="submit"
                                    class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                <span x-text="showCreateModal ? '添加' : '保存'"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 查看令牌模态框 -->
        <div x-show="showViewModal"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100 transform scale-100"
                 x-transition:leave-end="opacity-0 transform scale-95"
                 class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-gray-900">令牌详情</h3>
                        <button @click="showViewModal = false" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">ID</label>
                                <p class="text-sm text-gray-900" x-text="viewTokenData.id"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                                <span :class="viewTokenData.is_used ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'"
                                      class="px-2 py-1 text-xs font-semibold rounded-full">
                                    <span x-text="viewTokenData.is_used ? '已使用' : '未使用'"></span>
                                </span>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                            <p class="text-sm text-gray-900 break-all" x-text="viewTokenData.email"></p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Session</label>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="text-sm text-gray-900 break-all font-mono" x-text="viewTokenData.session || '-'"></p>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">授权验证URL</label>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="text-sm text-gray-900 break-all" x-text="viewTokenData.auth_url || '-'"></p>
                                <template x-if="viewTokenData.auth_url">
                                    <a :href="viewTokenData.auth_url" target="_blank"
                                       class="inline-flex items-center mt-2 text-blue-600 hover:text-blue-800 text-xs">
                                        <i class="fas fa-external-link-alt mr-1"></i>
                                        打开链接
                                    </a>
                                </template>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Code Verifier</label>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="text-sm text-gray-900 break-all font-mono" x-text="viewTokenData.code_verifier || '-'"></p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">创建时间</label>
                                <p class="text-sm text-gray-900" x-text="formatDateTime(viewTokenData.created_at)"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">更新时间</label>
                                <p class="text-sm text-gray-900" x-text="formatDateTime(viewTokenData.updated_at)"></p>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end gap-3 mt-6">
                        <button @click="copyToken(viewTokenData)"
                                class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700">
                            <i class="fas fa-copy mr-2"></i>复制
                        </button>
                        <button @click="editToken(viewTokenData)"
                                class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                            <i class="fas fa-edit mr-2"></i>编辑
                        </button>
                        <button @click="showViewModal = false"
                                class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div x-show="loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 flex items-center gap-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span>加载中...</span>
            </div>
        </div>

        <!-- 通知消息 -->
        <div x-show="notification.show"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 transform translate-y-2"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform translate-y-2"
             class="fixed top-4 right-4 z-50">
            <div :class="notification.type === 'success' ? 'bg-green-500' : 'bg-red-500'"
                 class="text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-3">
                <i :class="notification.type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'"></i>
                <span x-text="notification.message"></span>
                <button @click="notification.show = false" class="text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</body>
</html>


