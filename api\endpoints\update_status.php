<?php
/**
 * 更新令牌状态接口
 */

if (!isset($tokenId) || !is_numeric($tokenId)) {
    Utils::jsonResponse(null, 400, '无效的令牌ID');
}

$data = Utils::getPostData();

if (!isset($data['is_used'])) {
    Utils::jsonResponse(null, 400, '使用状态不能为空');
}

$isUsed = $data['is_used'] ? 1 : 0;

$tokenModel = new TokenModel();

// 检查令牌是否存在
$existingToken = $tokenModel->getTokenById($tokenId);
if (!$existingToken) {
    Utils::jsonResponse(null, 404, '令牌不存在');
}

$result = $tokenModel->updateTokenStatus($tokenId, $isUsed);

if ($result) {
    $token = $tokenModel->getTokenById($tokenId);
    Utils::jsonResponse($token, 200, '令牌状态更新成功');
} else {
    Utils::jsonResponse(null, 500, '令牌状态更新失败');
}
