<?php
/**
 * 创建令牌接口
 */

$data = Utils::getPostData();

// 验证必填字段
if (empty($data['email'])) {
    Utils::jsonResponse(null, 400, '邮箱不能为空');
}

if (!Utils::validateEmail($data['email'])) {
    Utils::jsonResponse(null, 400, '邮箱格式不正确');
}

// 清理输入数据
$cleanData = Utils::sanitizeInput($data);

$tokenModel = new TokenModel();
$tokenId = $tokenModel->createToken($cleanData);

if ($tokenId) {
    $token = $tokenModel->getTokenById($tokenId);
    Utils::jsonResponse($token, 201, '令牌创建成功');
} else {
    Utils::jsonResponse(null, 500, '令牌创建失败');
}
