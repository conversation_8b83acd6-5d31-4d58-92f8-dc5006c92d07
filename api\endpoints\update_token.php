<?php
/**
 * 更新令牌接口
 */

if (!isset($tokenId) || !is_numeric($tokenId)) {
    Utils::jsonResponse(null, 400, '无效的令牌ID');
}

$data = Utils::getPostData();

// 验证必填字段
if (empty($data['email'])) {
    Utils::jsonResponse(null, 400, '邮箱不能为空');
}

if (!Utils::validateEmail($data['email'])) {
    Utils::jsonResponse(null, 400, '邮箱格式不正确');
}

$tokenModel = new TokenModel();

// 检查令牌是否存在
$existingToken = $tokenModel->getTokenById($tokenId);
if (!$existingToken) {
    Utils::jsonResponse(null, 404, '令牌不存在');
}

// 清理输入数据
$cleanData = Utils::sanitizeInput($data);

$result = $tokenModel->updateToken($tokenId, $cleanData);

if ($result) {
    $token = $tokenModel->getTokenById($tokenId);
    Utils::jsonResponse($token, 200, '令牌更新成功');
} else {
    Utils::jsonResponse(null, 500, '令牌更新失败');
}
