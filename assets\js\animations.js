/**
 * 令牌管理系统 - 动画和交互效果
 */

// 数字计数动画
function animateNumber(element, start, end, duration = 1000) {
    const startTime = performance.now();
    const range = end - start;
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.round(start + (range * easeOutQuart));
        
        element.textContent = current;
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

// 统计卡片动画
function animateStatsCards() {
    const cards = document.querySelectorAll('.stats-card');
    cards.forEach((card, index) => {
        // 延迟动画，创建波浪效果
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            
            // 触发动画
            requestAnimationFrame(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            });
            
            // 数字计数动画
            const numberElement = card.querySelector('.stats-number');
            if (numberElement) {
                const finalValue = parseInt(numberElement.textContent) || 0;
                animateNumber(numberElement, 0, finalValue, 1500);
            }
        }, index * 100);
    });
}

// 表格行动画
function animateTableRows() {
    const rows = document.querySelectorAll('.modern-table tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateX(-20px)';
        row.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
        
        setTimeout(() => {
            row.style.opacity = '1';
            row.style.transform = 'translateX(0)';
        }, index * 50);
    });
}

// 页面加载动画
function initPageAnimations() {
    // 主容器淡入
    const mainContainer = document.querySelector('.main-container');
    if (mainContainer) {
        mainContainer.style.opacity = '0';
        mainContainer.style.transform = 'translateY(20px)';
        mainContainer.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
        
        setTimeout(() => {
            mainContainer.style.opacity = '1';
            mainContainer.style.transform = 'translateY(0)';
        }, 100);
    }
    
    // 标题动画
    const title = document.querySelector('.page-title');
    if (title) {
        title.style.opacity = '0';
        title.style.transform = 'translateY(-20px)';
        title.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        
        setTimeout(() => {
            title.style.opacity = '1';
            title.style.transform = 'translateY(0)';
        }, 200);
    }
    
    // 副标题动画
    const subtitle = document.querySelector('.page-subtitle');
    if (subtitle) {
        subtitle.style.opacity = '0';
        subtitle.style.transform = 'translateY(-10px)';
        subtitle.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        
        setTimeout(() => {
            subtitle.style.opacity = '1';
            subtitle.style.transform = 'translateY(0)';
        }, 400);
    }
    
    // 统计卡片动画
    setTimeout(() => {
        animateStatsCards();
    }, 600);
    
    // 搜索控制区域动画
    const searchControls = document.querySelector('.search-controls');
    if (searchControls) {
        searchControls.style.opacity = '0';
        searchControls.style.transform = 'translateY(20px)';
        searchControls.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        
        setTimeout(() => {
            searchControls.style.opacity = '1';
            searchControls.style.transform = 'translateY(0)';
        }, 1200);
    }
    
    // 表格动画
    const table = document.querySelector('.modern-table');
    if (table) {
        table.style.opacity = '0';
        table.style.transform = 'translateY(20px)';
        table.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        
        setTimeout(() => {
            table.style.opacity = '1';
            table.style.transform = 'translateY(0)';
            
            // 表格行动画
            setTimeout(() => {
                animateTableRows();
            }, 200);
        }, 1400);
    }
}

// 按钮点击波纹效果
function addRippleEffect(button, event) {
    const ripple = document.createElement('span');
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');
    
    button.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// 初始化交互效果
function initInteractionEffects() {
    // 为所有现代按钮添加波纹效果
    document.addEventListener('click', (event) => {
        if (event.target.closest('.btn-modern, .action-btn')) {
            const button = event.target.closest('.btn-modern, .action-btn');
            addRippleEffect(button, event);
        }
    });
    
    // 统计卡片悬停效果增强
    document.querySelectorAll('.stats-card').forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // 表格行悬停效果
    document.querySelectorAll('.modern-table tbody tr').forEach(row => {
        row.addEventListener('mouseenter', () => {
            row.style.transform = 'scale(1.01)';
            row.style.zIndex = '10';
        });
        
        row.addEventListener('mouseleave', () => {
            row.style.transform = 'scale(1)';
            row.style.zIndex = 'auto';
        });
    });
}

// 滚动动画
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // 观察需要动画的元素
    document.querySelectorAll('.stats-card, .search-controls, .modern-table').forEach(el => {
        observer.observe(el);
    });
}

// 通知动画
function showNotification(type, message) {
    const notification = document.createElement('div');
    notification.className = `notification ${type} show`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle mr-2"></i>
        ${message}
    `;
    
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// 加载动画
function showLoading() {
    const loading = document.createElement('div');
    loading.className = 'loading-overlay';
    loading.innerHTML = '<div class="loading-spinner"></div>';
    document.body.appendChild(loading);
    return loading;
}

function hideLoading(loadingElement) {
    if (loadingElement) {
        loadingElement.style.opacity = '0';
        setTimeout(() => {
            loadingElement.remove();
        }, 300);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    initPageAnimations();
    initInteractionEffects();
    initScrollAnimations();
});

// 导出函数供其他脚本使用
window.TokenAnimations = {
    animateNumber,
    animateStatsCards,
    animateTableRows,
    showNotification,
    showLoading,
    hideLoading
};
